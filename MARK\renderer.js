const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const fileInput = document.getElementById('fileInput');
const progressBar = document.getElementById('progressBar');
const resultList = document.getElementById('resultList');
const historyList = document.getElementById('historyList');

fileInput.addEventListener('change', async (e) => {
  const files = e.target.files;
  const formData = new FormData();
  for (const file of files) {
    formData.append('files', file);
  }

  try {
    const response = await fetch('http://localhost:3000/upload', {
      method: 'POST',
      body: formData
    });
    const data = await response.json();
    updateResults(data.results);
    updateHistory(data.history);
  } catch (error) {
    showError(`上传失败: ${error.message}`);
  }
});

function updateResults(results) {
  resultList.innerHTML = '';
  results.forEach(({ filename, success, message }) => {
    const item = document.createElement('div');
    item.className = `result-item ${success ? 'success' : 'error'}`;
    item.innerHTML = `
      <span>${filename}</span>
      <span>${success ? '✅' : '❌'}</span>
      <span>${message}</span>
    `;
    resultList.appendChild(item);
  });
}

function updateHistory(history) {
  historyList.innerHTML = '';
  history.forEach(({ time, file, output }) => {
    const item = document.createElement('div');
    item.className = 'history-item';
    item.innerHTML = `
      <span>${new Date(time).toLocaleString()}</span>
      <span>${file} → ${output}</span>
    `;
    historyList.appendChild(item);
  });
}

function showError(message) {
  const errorDiv = document.createElement('div');
  errorDiv.className = 'error-message';
  errorDiv.textContent = message;
  document.body.appendChild(errorDiv);
  setTimeout(() => errorDiv.remove(), 3000);
}