# 视频字幕提取器使用说明

## 功能特性

### 🎯 核心功能
- **字幕提取**: 从视频文件中提取现有字幕轨道
- **音频转录**: 使用Whisper对无字幕视频进行语音转录
- **智能翻译**: 集成GLM-4-Flash API进行字幕翻译
- **格式转换**: 将字幕转换为弹幕(.dm)格式
- **批量处理**: 支持目录下所有视频文件的批量处理

### 🔧 支持格式
- **视频格式**: MP4, AVI, MKV, MOV, WMV, FLV, M4V, WebM, TS, MTS
- **字幕格式**: SRT, ASS, SSA, VTT, SUB
- **输出格式**: DM弹幕格式, SRT字幕格式

## 安装依赖

### 方法一：自动安装（推荐）
```bash
# 运行依赖安装脚本
install_dependencies.bat
```

### 方法二：手动安装
```bash
# 安装Python依赖
pip install requests openai-whisper torch torchvision torchaudio

# 安装FFmpeg
# Windows: 下载并添加到PATH
# 或使用包管理器: choco install ffmpeg
```

## 使用方法

### 快速开始
1. 将视频文件放入 `vidoin` 目录
2. 运行 `extract_subtitles.bat`
3. 选择处理模式

### 命令行使用

#### 基本字幕提取
```bash
python video_subtitle_extractor.py
```

#### 启用转录功能（无字幕视频）
```bash
python video_subtitle_extractor.py --enable-transcribe
```

#### 启用翻译功能
```bash
python video_subtitle_extractor.py --translate --openai-api-key "your_api_key"
```

#### 完整功能（转录+翻译）
```bash
python video_subtitle_extractor.py \
  --enable-transcribe \
  --translate \
  --openai-api-key "7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0" \
  --openai-base-url "https://open.bigmodel.cn/api/paas/v4"
```

### 参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `--dir` | 视频目录 | `--dir vidoin` |
| `--enable-transcribe` | 启用转录功能 | `--enable-transcribe` |
| `--transcribe-method` | 转录方法 | `--transcribe-method whisper` |
| `--transcribe-language` | 转录语言 | `--transcribe-language zh` |
| `--translate` | 启用翻译 | `--translate` |
| `--openai-api-key` | GLM API密钥 | `--openai-api-key "your_key"` |
| `--openai-base-url` | GLM API地址 | `--openai-base-url "https://..."` |
| `--process-type` | 处理类型 | `--process-type clean` |
| `--keep-srt` | 保留SRT文件 | `--keep-srt` |
| `--verbose` | 详细输出 | `--verbose` |

## 使用场景

### 场景1：提取现有字幕
适用于已有字幕轨道的视频文件
```bash
python video_subtitle_extractor.py --dir vidoin
```

### 场景2：转录无字幕视频
适用于没有字幕的视频文件
```bash
python video_subtitle_extractor.py --enable-transcribe --transcribe-language zh
```

### 场景3：翻译外语字幕
适用于需要翻译的外语字幕
```bash
python video_subtitle_extractor.py --translate --openai-api-key "your_key"
```

### 场景4：完整处理流程
转录+翻译+清理
```bash
python video_subtitle_extractor.py \
  --enable-transcribe \
  --translate \
  --process-type clean \
  --keep-srt \
  --openai-api-key "your_key"
```

## 输出文件说明

### 文件命名规则
- 原始字幕: `视频名_sub0.dm`
- 转录字幕: `视频名_transcribed.dm`
- 翻译字幕: `视频名_translated.dm`
- 处理字幕: `视频名_clean.dm` 或 `视频名_enhance.dm`

### DM弹幕格式
```
时间,类型,字号,颜色,时间戳,弹幕池,用户ID,弹幕ID,内容
0.000,1,25,16777215,0,0,user,0,字幕内容
```

## 故障排除

### 常见问题

#### 1. FFmpeg未找到
```
错误: FFmpeg未安装或不可用
解决: 运行 install_dependencies.bat 或手动安装FFmpeg
```

#### 2. Whisper安装失败
```
错误: 无法导入whisper模块
解决: pip install openai-whisper torch
```

#### 3. 转录速度慢
```
原因: Whisper模型较大
解决: 使用较小的模型或GPU加速
```

#### 4. API调用失败
```
错误: GLM API调用失败
解决: 检查API密钥和网络连接
```

### 日志文件
详细错误信息请查看 `subtitle_extractor.log`

## 高级配置

### Whisper模型选择
可在代码中修改模型大小：
- `tiny`: 最快，准确度较低
- `base`: 平衡选择（默认）
- `small`: 较好准确度
- `medium`: 高准确度
- `large`: 最高准确度，最慢

### GLM API配置
```python
# 在代码中可自定义API参数
translator = GLMTranslator(
    api_key="your_key",
    base_url="https://open.bigmodel.cn/api/paas/v4"
)
```

## 注意事项

1. **版权声明**: 请确保您有权处理这些视频文件
2. **文件大小**: 大文件转录可能需要较长时间
3. **网络要求**: 翻译功能需要稳定的网络连接
4. **存储空间**: 确保有足够的磁盘空间存储输出文件
5. **语言支持**: Whisper支持多种语言，GLM主要支持中英文

## 更新日志

- v1.0: 基础字幕提取功能
- v1.1: 添加Whisper转录支持
- v1.2: 集成GLM-4-Flash翻译
- v1.3: 添加批量处理和错误处理
