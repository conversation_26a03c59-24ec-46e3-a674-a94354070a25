#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频字幕提取器测试脚本
"""

import sys
import subprocess
from pathlib import Path

def test_dependencies():
    """测试依赖是否正确安装"""
    print("=== 依赖检查 ===")
    
    # 检查Python模块
    modules = ['requests', 'pathlib', 'argparse']
    for module in modules:
        try:
            __import__(module)
            print(f"✓ {module} - 已安装")
        except ImportError:
            print(f"✗ {module} - 未安装")
    
    # 检查Whisper（可选）
    try:
        import whisper
        print(f"✓ whisper - 已安装")
    except ImportError:
        print(f"⚠ whisper - 未安装（转录功能不可用）")
    
    # 检查FFmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"✓ FFmpeg - 已安装")
        else:
            print(f"✗ FFmpeg - 安装异常")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print(f"✗ FFmpeg - 未安装")

def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 基本功能测试 ===")
    
    # 导入主模块
    try:
        from video_subtitle_extractor import VideoSubtitleExtractor, GLMTranslator, AudioTranscriber
        print("✓ 主模块导入成功")
    except ImportError as e:
        print(f"✗ 主模块导入失败: {e}")
        return
    
    # 测试提取器初始化
    try:
        extractor = VideoSubtitleExtractor("vidoin")
        print("✓ 提取器初始化成功")
    except Exception as e:
        print(f"✗ 提取器初始化失败: {e}")
        return
    
    # 测试FFmpeg检查
    if extractor.check_ffmpeg():
        print("✓ FFmpeg检查通过")
    else:
        print("⚠ FFmpeg检查失败")
    
    # 测试目录扫描
    video_files = extractor.get_video_files()
    print(f"✓ 找到 {len(video_files)} 个视频文件")

def test_glm_translator():
    """测试GLM翻译器"""
    print("\n=== GLM翻译器测试 ===")
    
    # 这里只测试初始化，不进行实际API调用
    try:
        from video_subtitle_extractor import GLMTranslator
        translator = GLMTranslator("test_key", "https://test.com")
        print("✓ GLM翻译器初始化成功")
    except Exception as e:
        print(f"✗ GLM翻译器初始化失败: {e}")

def test_transcriber():
    """测试转录器"""
    print("\n=== 转录器测试 ===")
    
    try:
        from video_subtitle_extractor import AudioTranscriber
        transcriber = AudioTranscriber("whisper")
        print("✓ 转录器初始化成功")
        
        # 测试Whisper检查
        if transcriber.check_whisper():
            print("✓ Whisper可用")
        else:
            print("⚠ Whisper不可用")
            
    except Exception as e:
        print(f"✗ 转录器测试失败: {e}")

def create_test_environment():
    """创建测试环境"""
    print("\n=== 创建测试环境 ===")
    
    # 确保vidoin目录存在
    vidoin_dir = Path("vidoin")
    vidoin_dir.mkdir(exist_ok=True)
    print(f"✓ 创建目录: {vidoin_dir}")
    
    # 创建测试用的README
    readme_path = vidoin_dir / "README.md"
    if not readme_path.exists():
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write("# 测试目录\n\n请将视频文件放入此目录进行测试。")
        print(f"✓ 创建文件: {readme_path}")

def main():
    """主测试函数"""
    print("视频字幕提取器 - 功能测试")
    print("=" * 40)
    
    test_dependencies()
    test_basic_functionality()
    test_glm_translator()
    test_transcriber()
    create_test_environment()
    
    print("\n=== 测试完成 ===")
    print("如果所有测试都通过，您可以开始使用字幕提取器。")
    print("如果有测试失败，请运行 install_dependencies.bat 安装依赖。")

if __name__ == "__main__":
    main()
