# 快速启动脚本 - 智能视频字幕提取器

param(
    [string]$Mode = "smart",
    [string]$ApiKey = "7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0"
)

Write-Host "智能视频字幕提取器 - 快速启动" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# 检查vidoin目录
if (-not (Test-Path "vidoin")) {
    Write-Host "创建vidoin目录..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Name "vidoin" | Out-Null
}

# 检查视频文件
$videoFiles = Get-ChildItem -Path "vidoin" -Include "*.mp4", "*.avi", "*.mkv", "*.mov", "*.wmv", "*.flv" -Recurse
if ($videoFiles.Count -eq 0) {
    Write-Host "请将视频文件放入vidoin目录" -ForegroundColor Yellow
    return
}

Write-Host "找到 $($videoFiles.Count) 个视频文件" -ForegroundColor Green

# 根据模式执行
switch ($Mode.ToLower()) {
    "extract" {
        Write-Host "模式: 仅提取现有字幕" -ForegroundColor Yellow
        python video_subtitle_extractor.py --dir vidoin --verbose --disable-auto-translate
    }
    "transcribe" {
        Write-Host "模式: 转录无字幕视频" -ForegroundColor Yellow
        python video_subtitle_extractor.py --dir vidoin --verbose --enable-transcribe --disable-auto-translate
    }
    "smart" {
        Write-Host "模式: 智能处理（中文直接转录，英文转录后翻译）" -ForegroundColor Green
        python video_subtitle_extractor.py --dir vidoin --verbose --enable-transcribe --openai-api-key "$ApiKey" --openai-base-url "https://open.bigmodel.cn/api/paas/v4"
    }
    "translate" {
        Write-Host "模式: 强制翻译所有内容" -ForegroundColor Yellow
        python video_subtitle_extractor.py --dir vidoin --verbose --enable-transcribe --force-translate --openai-api-key "$ApiKey" --openai-base-url "https://open.bigmodel.cn/api/paas/v4"
    }
    default {
        Write-Host "未知模式，使用智能模式" -ForegroundColor Yellow
        python video_subtitle_extractor.py --dir vidoin --verbose --enable-transcribe --openai-api-key "$ApiKey" --openai-base-url "https://open.bigmodel.cn/api/paas/v4"
    }
}

Write-Host "处理完成！" -ForegroundColor Green
