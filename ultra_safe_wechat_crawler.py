#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超安全微信公众号文章爬虫
专门应对微信名誉保护和强力反爬机制
"""

import requests
import re
import os
import time
import json
import random
from datetime import datetime
from typing import List, Dict, Set
from urllib.parse import quote, unquote, urlparse
from bs4 import BeautifulSoup
import hashlib
from pathlib import Path

class UltraSafeWeChatCrawler:
    def __init__(self, output_dir: str = "C:/Users/<USER>/Documents/Obsidian Vault/微信好文"):
        """
        超安全微信爬虫 - 专门应对反爬机制

        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.crawled_urls: Set[str] = set()
        self.articles: List[Dict] = []
        self.account_info = {}

        # 超保守的延迟设置
        self.min_delay = 10  # 最小10秒
        self.max_delay = 20  # 最大20秒
        self.search_delay = 30  # 搜索间隔30秒

        # 更真实的User-Agent池
        self.user_agents = [
            # Windows Chrome
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            # Windows Edge
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            # Windows Firefox
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/119.0',
            # Mac Chrome
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        ]

        # 创建会话
        self.session = requests.Session()
        self._setup_ultra_safe_session()

        # 请求计数器
        self.request_count = 0
        self.last_request_time = 0

    def _setup_ultra_safe_session(self):
        """设置超安全会话参数"""
        # 基础请求头
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
        })

        # 设置超时
        self.session.timeout = 30

    def _get_ultra_safe_headers(self) -> dict:
        """获取超安全请求头"""
        headers = {
            'User-Agent': random.choice(self.user_agents),
            'Referer': random.choice([
                'https://mp.weixin.qq.com/',
                'https://weixin.qq.com/',
                'https://www.google.com/',
                'https://www.baidu.com/',
            ]),
        }

        # 随机添加一些可选头
        if random.random() > 0.5:
            headers['X-Requested-With'] = 'XMLHttpRequest'

        if random.random() > 0.7:
            headers['Sec-Ch-Ua'] = '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"'
            headers['Sec-Ch-Ua-Mobile'] = '?0'
            headers['Sec-Ch-Ua-Platform'] = '"Windows"'

        return headers

    def _ultra_safe_delay(self, operation_type: str = "normal"):
        """超安全延迟策略"""
        if operation_type == "search":
            delay = random.uniform(self.search_delay, self.search_delay + 10)
        else:
            delay = random.uniform(self.min_delay, self.max_delay)

        # 检查请求频率
        current_time = time.time()
        if self.last_request_time > 0:
            time_since_last = current_time - self.last_request_time
            if time_since_last < self.min_delay:
                additional_delay = self.min_delay - time_since_last + random.uniform(2, 5)
                delay += additional_delay

        print(f"⏱️  超安全延迟 {delay:.1f} 秒 ({operation_type})...")
        time.sleep(delay)

        self.last_request_time = time.time()
        self.request_count += 1

        # 每10个请求后额外休息
        if self.request_count % 10 == 0:
            extra_delay = random.uniform(60, 120)  # 1-2分钟
            print(f"🛡️  防护性休息 {extra_delay:.0f} 秒...")
            time.sleep(extra_delay)

    def _safe_request(self, url: str, operation_type: str = "normal") -> requests.Response:
        """超安全请求方法"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                # 超安全延迟
                self._ultra_safe_delay(operation_type)

                # 获取随机请求头
                headers = self._get_ultra_safe_headers()

                print(f"🌐 安全请求: {url[:50]}...")

                response = self.session.get(url, headers=headers, timeout=30)

                # 检查响应状态
                if response.status_code == 200:
                    # 检查是否被重定向到保护页面
                    if self._is_protection_page(response.text):
                        print(f"⚠️  检测到保护页面，等待更长时间...")
                        time.sleep(random.uniform(120, 180))  # 等待2-3分钟
                        continue

                    return response
                elif response.status_code == 429:
                    print(f"⚠️  请求过于频繁，等待更长时间...")
                    time.sleep(random.uniform(300, 600))  # 等待5-10分钟
                    continue
                else:
                    print(f"⚠️  HTTP {response.status_code}, 重试...")
                    time.sleep(random.uniform(30, 60))
                    continue

            except Exception as e:
                print(f"❌ 请求失败 (尝试 {attempt+1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(60, 120))
                continue

        raise Exception(f"请求失败，已重试 {max_retries} 次")

    def _is_protection_page(self, html_content: str) -> bool:
        """检测是否为保护页面"""
        protection_keywords = [
            '名誉保护投诉指引',
            '该内容已被发布者删除',
            '此内容因违规无法查看',
            '链接已过期',
            '访问过于频繁',
            '请稍后再试',
            '系统检测到异常',
            '验证码',
            'captcha',
            '人机验证',
        ]

        for keyword in protection_keywords:
            if keyword in html_content:
                return True

        return False

    def extract_account_info_safe(self, html_content: str) -> Dict:
        """安全提取账号信息"""
        account_info = {}

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 提取公众号名称
            name_selectors = [
                '#js_name',
                '.rich_media_meta_text',
                '.profile_nickname',
                '.account_nickname',
                'meta[property="og:title"]',
                'title'
            ]

            for selector in name_selectors:
                if selector.startswith('meta'):
                    elem = soup.select_one(selector)
                    if elem:
                        content = elem.get('content', '')
                        if content and len(content) > 1 and '微信' not in content:
                            account_info['name'] = content.strip()
                            break
                else:
                    elem = soup.select_one(selector)
                    if elem:
                        name = elem.get_text().strip()
                        if name and len(name) > 1 and '微信' not in name:
                            account_info['name'] = name
                            break

            # 提取biz参数
            biz_patterns = [
                r'__biz=([^&"\']+)',
                r'"biz":"([^"]+)"',
                r'biz=([^&\s]+)',
            ]

            for pattern in biz_patterns:
                match = re.search(pattern, html_content)
                if match:
                    biz = unquote(match.group(1))
                    if len(biz) > 10:  # biz通常比较长
                        account_info['biz'] = biz
                        break

            print(f"📱 提取账号信息: {account_info}")

        except Exception as e:
            print(f"❌ 提取账号信息失败: {e}")

        return account_info

    def search_with_ultra_safe_strategy(self, account_name: str, biz: str = None, max_results: int = 30) -> List[str]:
        """超安全搜索策略"""
        print(f"🔍 开始超安全搜索: {account_name}")
        all_urls = set()

        # 搜索策略：只使用最安全的方法
        search_methods = [
            self._search_baidu_ultra_safe,
            self._search_sogou_ultra_safe,
        ]

        for method in search_methods:
            try:
                print(f"🔍 使用 {method.__name__} 搜索...")
                urls = method(account_name, biz, max_results // len(search_methods))
                all_urls.update(urls)

                print(f"✅ {method.__name__} 发现 {len(urls)} 个URL")

                # 搜索间隔
                self._ultra_safe_delay("search")

                if len(all_urls) >= max_results:
                    break

            except Exception as e:
                print(f"❌ {method.__name__} 搜索失败: {e}")
                continue

        print(f"🎯 总共发现 {len(all_urls)} 个URL")
        return list(all_urls)[:max_results]

    def _search_baidu_ultra_safe(self, account_name: str, biz: str = None, max_results: int = 15) -> List[str]:
        """超安全百度搜索"""
        urls = set()

        # 更保守的搜索查询
        queries = [
            f'site:mp.weixin.qq.com "{account_name}"',
        ]

        if biz:
            queries.append(f'site:mp.weixin.qq.com __biz={biz[:20]}')  # 只用biz的前20个字符

        for query in queries[:1]:  # 只用第一个查询，减少请求
            try:
                search_url = f"https://www.baidu.com/s?wd={quote(query)}&pn=0&rn=20"

                response = self._safe_request(search_url, "search")
                soup = BeautifulSoup(response.text, 'html.parser')

                # 提取搜索结果
                for link in soup.find_all('a', href=True):
                    href = link.get('href', '')
                    if 'mp.weixin.qq.com/s' in href and 'baidu.com' not in href:
                        clean_url = self._extract_clean_url(href)
                        if clean_url and self._validate_wechat_url(clean_url):
                            urls.add(clean_url)
                            if len(urls) >= max_results:
                                break

                if len(urls) >= max_results:
                    break

            except Exception as e:
                print(f"❌ 百度搜索查询失败: {e}")
                continue

        return list(urls)

    def _search_sogou_ultra_safe(self, account_name: str, biz: str = None, max_results: int = 15) -> List[str]:
        """超安全搜狗搜索"""
        urls = set()

        try:
            # 搜狗微信搜索
            search_url = f"https://weixin.sogou.com/weixin?type=1&query={quote(account_name)}"

            response = self._safe_request(search_url, "search")
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找文章链接
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if 'mp.weixin.qq.com' in href:
                    clean_url = self._extract_clean_url(href)
                    if clean_url and self._validate_wechat_url(clean_url):
                        urls.add(clean_url)
                        if len(urls) >= max_results:
                            break

        except Exception as e:
            print(f"❌ 搜狗搜索失败: {e}")

        return list(urls)

    def _extract_clean_url(self, url: str) -> str:
        """提取干净的URL"""
        if not url:
            return ""

        # 处理百度重定向
        if 'baidu.com' in url and 'url=' in url:
            try:
                from urllib.parse import parse_qs, urlparse
                parsed = urlparse(url)
                params = parse_qs(parsed.query)
                if 'url' in params:
                    return params['url'][0]
            except:
                pass

        # 直接返回微信URL
        if url.startswith('http') and 'mp.weixin.qq.com' in url:
            return url

        return ""

    def _validate_wechat_url(self, url: str) -> bool:
        """验证微信URL"""
        if not url or not url.startswith(('http://', 'https://')):
            return False

        if 'mp.weixin.qq.com/s' not in url:
            return False

        # 避免明显的错误URL
        invalid_patterns = [
            'baidu.com',
            'sogou.com',
            'google.com',
            'bing.com',
        ]

        for pattern in invalid_patterns:
            if pattern in url:
                return False

        return True

    def crawl_single_article_ultra_safe(self, url: str) -> Dict:
        """超安全单篇文章爬取"""
        try:
            print(f"📄 超安全爬取: {url}")

            response = self._safe_request(url, "article")

            # 检查是否为保护页面
            if self._is_protection_page(response.text):
                print(f"⚠️  检测到保护页面，跳过此URL")
                return None

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取账号信息（如果是第一篇）
            if not self.account_info:
                self.account_info = self.extract_account_info_safe(response.text)

            # 提取文章信息
            article_info = self._extract_article_info_safe(soup, url)

            if article_info:
                print(f"✅ 成功爬取: {article_info['title']}")
                return article_info

            return None

        except Exception as e:
            print(f"❌ 爬取文章失败 {url}: {e}")
            return None

    def _extract_article_info_safe(self, soup, url: str) -> Dict:
        """安全提取文章信息"""
        try:
            # 提取标题
            title_selectors = [
                'h1.rich_media_title',
                'h1#activity-name',
                'h1',
                '.rich_media_title',
                'meta[property="og:title"]',
                'title'
            ]

            title = "未知标题"
            for selector in title_selectors:
                if selector.startswith('meta'):
                    elem = soup.select_one(selector)
                    if elem:
                        title = elem.get('content', '').strip()
                        if title and len(title) > 3 and '微信' not in title:
                            break
                else:
                    elem = soup.select_one(selector)
                    if elem:
                        title = elem.get_text().strip()
                        if title and len(title) > 3:
                            break

            # 提取作者
            author = self.account_info.get('name', '未知作者')
            author_selectors = ['.rich_media_meta_text', '.profile_nickname', '#js_name']

            for selector in author_selectors:
                elem = soup.select_one(selector)
                if elem:
                    author_text = elem.get_text().strip()
                    if author_text and '微信' not in author_text:
                        author = author_text
                        break

            # 提取发布时间
            publish_time = self._extract_publish_time_safe(soup)

            # 提取内容
            content = self._extract_content_safe(soup)

            # 检查是否为有效文章
            if self._is_valid_article(title, content):
                return {
                    'title': title,
                    'author': author,
                    'publish_time': publish_time,
                    'content': content,
                    'url': url,
                    'crawl_time': datetime.now().isoformat()
                }

            return None

        except Exception as e:
            print(f"❌ 提取文章信息失败: {e}")
            return None

    def _extract_publish_time_safe(self, soup) -> str:
        """安全提取发布时间"""
        time_selectors = [
            '#publish_time',
            '.rich_media_meta_text',
            '[id*="time"]',
            'meta[property="article:published_time"]'
        ]

        for selector in time_selectors:
            if selector.startswith('meta'):
                elem = soup.select_one(selector)
                if elem:
                    time_text = elem.get('content', '').strip()
                    if re.search(r'\d{4}[-年]\d{1,2}[-月]\d{1,2}', time_text):
                        return time_text
            else:
                elem = soup.select_one(selector)
                if elem:
                    time_text = elem.get_text().strip()
                    if re.search(r'\d{4}[-年]\d{1,2}[-月]\d{1,2}', time_text):
                        return time_text

        return datetime.now().strftime('%Y-%m-%d')

    def _extract_content_safe(self, soup) -> str:
        """安全提取文章内容"""
        content_selectors = [
            '#js_content',
            '.rich_media_content',
            '.content',
            'article',
            '.post-content'
        ]

        for selector in content_selectors:
            elem = soup.select_one(selector)
            if elem:
                # 移除脚本和样式
                for script in elem(["script", "style", "noscript"]):
                    script.decompose()

                text = elem.get_text()
                lines = (line.strip() for line in text.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                content = '\n'.join(chunk for chunk in chunks if chunk)

                if len(content) > 100:  # 确保内容足够长
                    return content

        return "无法提取内容"

    def _is_valid_article(self, title: str, content: str) -> bool:
        """检查是否为有效文章"""
        # 检查标题
        invalid_title_keywords = [
            '名誉保护',
            '投诉指引',
            '系统提示',
            '访问异常',
            '验证码',
            '人机验证',
            '页面不存在',
            '链接已过期'
        ]

        for keyword in invalid_title_keywords:
            if keyword in title:
                return False

        # 检查内容长度
        if len(content) < 50:
            return False

        # 检查内容关键词
        invalid_content_keywords = [
            '名誉保护投诉指引',
            '该内容已被发布者删除',
            '此内容因违规无法查看',
            '访问过于频繁',
            '请稍后再试'
        ]

        for keyword in invalid_content_keywords:
            if keyword in content:
                return False

        return True

    def save_article_safe(self, article: Dict) -> str:
        """安全保存文章"""
        try:
            safe_title = re.sub(r'[<>:"/\\|?*]', '_', article['title'])
            safe_title = safe_title[:100]

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{safe_title}_{timestamp}.md"
            filepath = self.output_dir / filename

            counter = 1
            while filepath.exists():
                filename = f"{safe_title}_{timestamp}_{counter}.md"
                filepath = self.output_dir / filename
                counter += 1

            markdown_content = f"""# {article['title']}

**作者:** {article['author']}
**发布时间:** {article['publish_time']}
**原文链接:** {article['url']}
**爬取时间:** {article['crawl_time']}

---

{article['content']}

---

*本文由超安全微信爬虫抓取*
"""

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            print(f"💾 文章已保存: {filename}")
            return str(filepath)

        except Exception as e:
            print(f"❌ 保存文章失败: {e}")
            return None

    def crawl_articles_ultra_safe(self, start_url: str, max_articles: int = 20) -> List[str]:
        """超安全文章爬取主函数"""
        print(f"🛡️  启动超安全微信爬虫")
        print(f"📄 起始URL: {start_url}")
        print(f"🎯 目标文章数: {max_articles} (保守设置)")
        print(f"⏱️  延迟设置: {self.min_delay}-{self.max_delay}秒")
        print("-" * 80)

        saved_files = []

        # 第一阶段：爬取起始文章
        print("📍 第一阶段：超安全爬取起始文章...")
        first_article = self.crawl_single_article_ultra_safe(start_url)

        if first_article:
            self.articles.append(first_article)
            self.crawled_urls.add(start_url)

            saved_file = self.save_article_safe(first_article)
            if saved_file:
                saved_files.append(saved_file)
        else:
            print("❌ 起始文章爬取失败，可能遇到保护机制")
            return []

        # 第二阶段：超保守搜索
        if self.account_info.get('name') and len(self.articles) < max_articles:
            account_name = self.account_info['name']
            biz = self.account_info.get('biz')

            print(f"\n🔍 第二阶段：超安全搜索 '{account_name}' 的文章...")

            # 减少搜索数量，避免触发保护
            search_urls = self.search_with_ultra_safe_strategy(
                account_name, biz, min(max_articles * 2, 20)
            )

            print(f"🎯 搜索发现 {len(search_urls)} 个URL")

            # 第三阶段：超保守爬取
            print(f"\n📚 第三阶段：超安全批量爬取...")

            for i, url in enumerate(search_urls):
                if len(self.articles) >= max_articles:
                    print(f"🎯 已达到目标文章数量: {max_articles}")
                    break

                if url in self.crawled_urls:
                    continue

                print(f"\n📊 进度: {len(self.articles)}/{max_articles} | 处理第 {i+1}/{len(search_urls)} 个URL")

                article = self.crawl_single_article_ultra_safe(url)

                if article:
                    # 验证文章是否属于目标账号
                    if self._is_target_account_article_safe(article):
                        self.articles.append(article)
                        self.crawled_urls.add(url)

                        saved_file = self.save_article_safe(article)
                        if saved_file:
                            saved_files.append(saved_file)
                    else:
                        print(f"⚠️  文章不属于目标账号，跳过")

        # 生成总结
        self.generate_summary_safe(saved_files)

        print(f"\n🎉 超安全爬取完成！")
        print(f"📊 成功爬取: {len(self.articles)} 篇文章")
        print(f"📁 保存目录: {self.output_dir}")
        print(f"🛡️  总请求数: {self.request_count}")

        return saved_files

    def _is_target_account_article_safe(self, article: Dict) -> bool:
        """安全验证文章是否属于目标账号"""
        if not self.account_info.get('name'):
            return True

        target_name = self.account_info['name']
        article_author = article.get('author', '')
        article_title = article.get('title', '')

        # 检查作者名称
        if target_name in article_author or article_author in target_name:
            return True

        # 检查URL中的biz参数
        if self.account_info.get('biz'):
            target_biz = self.account_info['biz']
            article_url = article.get('url', '')
            if target_biz in article_url:
                return True

        # 避免明显的错误文章
        invalid_indicators = ['名誉保护', '投诉指引', '系统提示']
        for indicator in invalid_indicators:
            if indicator in article_title:
                return False

        return True

    def generate_summary_safe(self, saved_files: List[str]):
        """生成安全爬取总结"""
        try:
            account_name = self.account_info.get('name', '未知公众号')

            summary_content = f"""# 超安全微信爬虫 - 爬取总结

**公众号:** {account_name}
**爬取时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**总文章数:** {len(self.articles)}
**保存目录:** {self.output_dir}

## 安全策略
- 🛡️ 超保守延迟: {self.min_delay}-{self.max_delay}秒
- 🔍 限制搜索数量: 避免触发保护
- 📊 智能保护检测: 自动识别并跳过保护页面
- ⏱️ 防护性休息: 每10个请求休息1-2分钟

## 账号信息
- **名称:** {account_name}
- **BIZ参数:** {self.account_info.get('biz', '未获取')}

## 技术统计
- **总请求数:** {self.request_count}
- **已爬取URL:** {len(self.crawled_urls)}
- **成功率:** {len(self.articles)/max(len(self.crawled_urls), 1)*100:.1f}%

## 文章列表

"""

            for i, article in enumerate(self.articles, 1):
                summary_content += f"{i}. **{article['title']}**\n"
                summary_content += f"   - 作者: {article['author']}\n"
                summary_content += f"   - 发布时间: {article['publish_time']}\n"
                summary_content += f"   - 链接: {article['url']}\n\n"

            summary_content += f"""
## 保存的文件

"""

            for i, filepath in enumerate(saved_files, 1):
                filename = Path(filepath).name
                summary_content += f"{i}. {filename}\n"

            summary_content += f"""

---
*由超安全微信爬虫生成 - 专门应对反爬机制*
"""

            summary_path = self.output_dir / f"超安全爬取总结_{account_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"

            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary_content)

            print(f"📋 总结文件已保存: {summary_path.name}")

        except Exception as e:
            print(f"❌ 生成总结失败: {e}")


def main():
    """主函数"""
    print("=== 超安全微信公众号文章爬虫 ===")
    print("专门应对微信名誉保护和强力反爬机制")
    print()

    start_url = input("请输入微信文章URL: ").strip()

    if not start_url:
        print("❌ URL不能为空")
        return

    if 'mp.weixin.qq.com' not in start_url:
        print("❌ 请输入有效的微信公众号文章URL")
        return

    max_articles = input("请输入要爬取的文章数量 (建议20以内): ").strip()
    try:
        max_articles = int(max_articles) if max_articles else 20
        if max_articles > 50:
            print("⚠️  建议不超过50篇，避免触发保护机制")
            max_articles = 50
    except ValueError:
        max_articles = 20

    output_dir = input("请输入输出目录 (默认: C:/Users/<USER>/Documents/Obsidian Vault/微信好文): ").strip()
    if not output_dir:
        output_dir = "C:/Users/<USER>/Documents/Obsidian Vault/微信好文"

    # 创建超安全爬虫实例
    crawler = UltraSafeWeChatCrawler(output_dir=output_dir)

    print(f"\n🛡️  超安全设置:")
    print(f"   ⏱️ 延迟范围: {crawler.min_delay}-{crawler.max_delay}秒")
    print(f"   🔍 搜索间隔: {crawler.search_delay}秒")
    print(f"   🛡️ 防护休息: 每10个请求休息1-2分钟")
    print(f"   📊 保护检测: 自动识别并跳过保护页面")

    try:
        # 开始超安全爬取
        saved_files = crawler.crawl_articles_ultra_safe(start_url, max_articles)

        if saved_files:
            print(f"\n✅ 超安全爬取成功完成!")
            print(f"📁 文件保存在: {output_dir}")
            print(f"📊 共保存 {len(saved_files)} 篇文章")
        else:
            print(f"\n❌ 爬取失败，可能遇到强力保护机制")
            print(f"💡 建议:")
            print(f"   1. 等待更长时间后重试")
            print(f"   2. 尝试不同的起始文章")
            print(f"   3. 减少目标文章数量")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断了爬取过程")
    except Exception as e:
        print(f"\n❌ 爬取过程出错: {e}")


if __name__ == "__main__":
    main()