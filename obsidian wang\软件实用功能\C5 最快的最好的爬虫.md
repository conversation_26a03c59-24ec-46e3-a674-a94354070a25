#!/usr/bin/env python3  
# -*- coding: utf-8 -*-  
"""  
微信公众号文章爬虫命令行工具  
支持自定义URL和输出目录  
"""  
  
import argparse  
import sys  
import os  
from wechat_crawler_with_images import WeChatCrawlerWithImages  
  
def main():  
"""命令行主函数"""  
parser = argparse.ArgumentParser(  
description='微信公众号文章爬虫 - 爬取指定文章及其相关文章',  
formatter_class=argparse.RawDescriptionHelpFormatter,  
epilog="""  
使用示例:  
python wechat_crawler_cli.py -u "https://mp.weixin.qq.com/s/qJN0eQliHRFl2WbIt4czSg"  
python wechat_crawler_cli.py -u "https://mp.weixin.qq.com/s/example" -n 30  
python wechat_crawler_cli.py -u "https://mp.weixin.qq.com/s/example" -o "E:/my_articles"  
"""  
)  
  
parser.add_argument(  
'-u', '--url',  
required=True,  
help='要爬取的微信文章URL'  
)  
  
parser.add_argument(  
'-n', '--num-articles',  
type=int,  
default=46,  
help='要爬取的文章数量 (默认: 46)'  
)  
  
parser.add_argument(  
'-o', '--output-dir',  
default="E:/mcp-test/obsidian wang/微信好文",  
help='输出目录 (默认: E:/mcp-test/obsidian wang/微信好文)'  
)  
  
parser.add_argument(  
'-v', '--verbose',  
action='store_true',  
help='显示详细输出'  
)  
  
args = parser.parse_args()  
  
# 验证URL  
if not args.url.startswith('http'):  
print("❌ 错误: URL必须以http或https开头")  
sys.exit(1)  
  
if 'mp.weixin.qq.com' not in args.url:  
print("❌ 错误: 请提供有效的微信公众号文章URL")  
sys.exit(1)  
  
# 显示配置信息  
print("🚀 微信公众号文章爬虫启动...")  
print(f"📄 起始URL: {args.url}")  
print(f"🎯 目标文章数: {args.num_articles}")  
print(f"📁 输出目录: {args.output_dir}")  
print("-" * 60)  
  
try:  
# 创建爬虫实例（默认支持图片下载）  
crawler = WeChatCrawlerWithImages(output_dir=args.output_dir)  
  
# 开始爬取  
saved_files = crawler.crawl_articles(args.url, max_articles=args.num_articles)  
  
# 生成总结  
summary_file = crawler.generate_summary(saved_files)  
  
# 显示结果  
print("\n" + "="*60)  
print("🎉 爬取任务完成！")  
print(f"📊 共爬取文章: {len(crawler.articles)} 篇")  
  
# 显示图片统计  
total_images = sum(article.get('image_count', 0) for article in crawler.articles)  
print(f"🖼️ 共下载图片: {total_images} 张")  
print(f"📁 保存目录: {crawler.output_dir}")  
print(f"🖼️ 图片目录: {crawler.images_dir}")  
  
if summary_file:  
print(f"📋 总结文件: {summary_file}")  
print("="*60)  
  
# 显示文章列表  
if args.verbose and crawler.articles:  
print("\n📚 爬取的文章列表:")  
for i, article in enumerate(crawler.articles, 1):  
image_info = ""  
if article.get('image_count', 0) > 0:  
image_info = f" (含 {article['image_count']} 张图片)"  
print(f" {i:2d}. {article['title']}{image_info}")  
  
except KeyboardInterrupt:  
print("\n⚠️ 用户中断了爬取过程")  
sys.exit(1)  
except Exception as e:  
print(f"\n❌ 爬取过程中出现错误: {str(e)}")  
sys.exit(1)  
  
  
if __name__ == "__main__":  
main()