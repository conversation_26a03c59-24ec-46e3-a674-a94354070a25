#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化微信公众号文章爬虫
支持从当前页面自动跳转到公众号主页并爬取所有文章
"""

import time
import re
import os
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import requests
from bs4 import BeautifulSoup

class AutoWeChatCrawler:
    def __init__(self, output_dir: str = "C:/Users/<USER>/Documents/Obsidian Vault/微信好文", headless: bool = False):
        """
        初始化自动化微信爬虫

        Args:
            output_dir: 输出目录
            headless: 是否无头模式运行
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.headless = headless
        self.driver = None
        self.wait = None
        self.crawled_urls = set()
        self.articles = []

        # 设置请求会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()

        if self.headless:
            chrome_options.add_argument('--headless')

        # 基本设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')

        # 模拟真实浏览器
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

        # 禁用图片加载以提高速度（可选）
        # chrome_options.add_argument('--blink-settings=imagesEnabled=false')

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            print("✅ Chrome浏览器驱动初始化成功")
            return True
        except Exception as e:
            print(f"❌ Chrome浏览器驱动初始化失败: {e}")
            print("💡 请确保已安装ChromeDriver并添加到PATH")
            return False

    def navigate_to_account_page(self, article_url: str) -> str:
        """从文章页面导航到公众号主页"""
        try:
            print(f"🌐 打开文章页面: {article_url}")
            self.driver.get(article_url)
            time.sleep(3)

            # 查找公众号名称链接的多种选择器
            account_selectors = [
                "a[href*='profile']",  # 公众号链接
                ".rich_media_meta_link",  # 作者链接
                "#js_name",  # 公众号名称
                "a.account_nickname",  # 账号昵称链接
                ".profile_nickname a",  # 个人资料昵称
            ]

            account_element = None
            account_url = None

            for selector in account_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        if href and ('profile' in href or 'account' in href):
                            account_element = element
                            account_url = href
                            break
                    if account_element:
                        break
                except:
                    continue

            if account_element and account_url:
                print(f"🎯 找到公众号链接: {account_url}")

                # 点击跳转到公众号主页
                self.driver.execute_script("arguments[0].click();", account_element)
                time.sleep(5)

                # 获取当前URL
                current_url = self.driver.current_url
                print(f"📱 已跳转到公众号主页: {current_url}")

                return current_url
            else:
                print("❌ 未找到公众号链接，尝试其他方法...")
                return self._try_alternative_navigation()

        except Exception as e:
            print(f"❌ 导航到公众号主页失败: {e}")
            return None

    def _try_alternative_navigation(self) -> str:
        """尝试其他导航方法"""
        try:
            # 方法1: 查找页面中的公众号信息
            page_source = self.driver.page_source

            # 提取biz参数
            biz_match = re.search(r'__biz=([^&"\']+)', page_source)
            if biz_match:
                biz = biz_match.group(1)
                print(f"🔍 提取到biz参数: {biz}")

                # 构建公众号主页URL
                profile_url = f"https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz={biz}"

                print(f"🌐 尝试访问构建的主页URL: {profile_url}")
                self.driver.get(profile_url)
                time.sleep(5)

                return self.driver.current_url

            # 方法2: 在当前页面查找更多文章链接
            print("🔍 在当前页面查找相关文章链接...")
            return self.driver.current_url

        except Exception as e:
            print(f"❌ 替代导航方法失败: {e}")
            return None

    def extract_article_links_from_page(self) -> List[str]:
        """从当前页面提取文章链接"""
        links = []

        try:
            # 等待页面加载
            time.sleep(3)

            # 滚动页面以加载更多内容
            self._scroll_page()

            # 查找文章链接的多种选择器
            link_selectors = [
                "a[href*='mp.weixin.qq.com/s']",  # 直接的文章链接
                ".weui-media-box__title a",  # 媒体框标题链接
                ".msg_item a",  # 消息项链接
                ".appmsg_item a",  # 应用消息项链接
                "h4 a",  # h4标题链接
                ".rich_media_title a",  # 富媒体标题链接
            ]

            for selector in link_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        if href and 'mp.weixin.qq.com/s' in href:
                            clean_href = self._clean_url(href)
                            if clean_href and clean_href not in links:
                                links.append(clean_href)
                except:
                    continue

            # 从页面源码中提取链接
            page_source = self.driver.page_source
            url_pattern = r'https://mp\.weixin\.qq\.com/s/[A-Za-z0-9_-]+'
            found_urls = re.findall(url_pattern, page_source)

            for url in found_urls:
                clean_url = self._clean_url(url)
                if clean_url and clean_url not in links:
                    links.append(clean_url)

            print(f"🎯 从页面提取到 {len(links)} 个文章链接")

            # 显示前几个链接作为预览
            for i, link in enumerate(links[:5]):
                print(f"   {i+1}. {link}")

            if len(links) > 5:
                print(f"   ... 还有 {len(links)-5} 个链接")

            return links

        except Exception as e:
            print(f"❌ 提取文章链接失败: {e}")
            return []

    def _scroll_page(self):
        """滚动页面以加载更多内容"""
        try:
            print("📜 滚动页面加载更多内容...")

            # 获取初始页面高度
            last_height = self.driver.execute_script("return document.body.scrollHeight")

            scroll_attempts = 0
            max_scrolls = 5

            while scroll_attempts < max_scrolls:
                # 滚动到页面底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)

                # 计算新的页面高度
                new_height = self.driver.execute_script("return document.body.scrollHeight")

                if new_height == last_height:
                    # 尝试点击"查看更多"按钮
                    try:
                        more_buttons = self.driver.find_elements(By.XPATH,
                            "//*[contains(text(), '查看更多') or contains(text(), '加载更多') or contains(text(), '更多文章')]")

                        for button in more_buttons:
                            if button.is_displayed():
                                self.driver.execute_script("arguments[0].click();", button)
                                time.sleep(3)
                                break
                    except:
                        pass

                    break

                last_height = new_height
                scroll_attempts += 1
                print(f"   滚动 {scroll_attempts}/{max_scrolls}")

            print("✅ 页面滚动完成")

        except Exception as e:
            print(f"❌ 页面滚动失败: {e}")

    def _clean_url(self, url: str) -> str:
        """清理URL"""
        if not url:
            return ""

        # 移除HTML实体
        url = url.replace('&amp;', '&')

        # 移除多余的参数，保留核心参数
        if '?' in url:
            base_url, params = url.split('?', 1)
            # 保留重要参数
            important_params = []
            for param in params.split('&'):
                if any(key in param for key in ['__biz', 'mid', 'idx', 'sn', 'chksm']):
                    important_params.append(param)

            if important_params:
                url = base_url + '?' + '&'.join(important_params)
            else:
                url = base_url

        return url.strip()

    def crawl_article_content(self, url: str) -> Dict:
        """爬取单篇文章内容"""
        try:
            print(f"📄 爬取文章: {url}")

            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取文章信息
            title = self._extract_title(soup)
            author = self._extract_author(soup)
            publish_time = self._extract_publish_time(soup)
            content = self._extract_content(soup)

            article_info = {
                'title': title,
                'author': author,
                'publish_time': publish_time,
                'content': content,
                'url': url,
                'crawl_time': datetime.now().isoformat()
            }

            print(f"✅ 成功爬取: {title}")
            return article_info

        except Exception as e:
            print(f"❌ 爬取文章失败 {url}: {e}")
            return None

    def _extract_title(self, soup) -> str:
        """提取文章标题"""
        selectors = ['h1.rich_media_title', 'h1#activity-name', 'h1', '.rich_media_title']

        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                title = element.get_text().strip()
                if title and len(title) > 3:
                    return title

        return f"文章_{int(time.time())}"

    def _extract_author(self, soup) -> str:
        """提取作者"""
        selectors = ['.rich_media_meta_text', '.profile_nickname', '#js_name']

        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                author = element.get_text().strip()
                if author:
                    return author

        return "未知作者"

    def _extract_publish_time(self, soup) -> str:
        """提取发布时间"""
        selectors = ['#publish_time', '.rich_media_meta_text']

        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                time_text = element.get_text().strip()
                if re.search(r'\d{4}[-年]\d{1,2}[-月]\d{1,2}', time_text):
                    return time_text

        return datetime.now().strftime('%Y-%m-%d')

    def _extract_content(self, soup) -> str:
        """提取文章内容"""
        selectors = ['#js_content', '.rich_media_content', '.content']

        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                # 移除脚本和样式
                for script in element(["script", "style"]):
                    script.decompose()

                # 获取文本内容
                text = element.get_text()
                lines = (line.strip() for line in text.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                content = '\n'.join(chunk for chunk in chunks if chunk)

                return content

        return "无法提取内容"

    def save_article(self, article: Dict) -> str:
        """保存文章到Markdown文件"""
        try:
            # 清理文件名
            safe_title = re.sub(r'[<>:"/\\|?*]', '_', article['title'])
            safe_title = safe_title[:100]  # 限制长度

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{safe_title}_{timestamp}.md"
            filepath = self.output_dir / filename

            # 避免文件名冲突
            counter = 1
            while filepath.exists():
                filename = f"{safe_title}_{timestamp}_{counter}.md"
                filepath = self.output_dir / filename
                counter += 1

            # 生成Markdown内容
            markdown_content = f"""# {article['title']}

**作者:** {article['author']}
**发布时间:** {article['publish_time']}
**原文链接:** {article['url']}
**爬取时间:** {article['crawl_time']}

---

{article['content']}

---

*本文由自动化微信爬虫抓取*
"""

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            print(f"💾 文章已保存: {filename}")
            return str(filepath)

        except Exception as e:
            print(f"❌ 保存文章失败: {e}")
            return None

    def auto_crawl_from_article(self, article_url: str, max_articles: int = 50) -> List[str]:
        """从单篇文章开始自动爬取整个公众号"""
        print(f"🚀 开始自动化爬取，起始文章: {article_url}")
        print(f"🎯 目标文章数: {max_articles}")

        if not self.setup_driver():
            return []

        saved_files = []

        try:
            # 1. 导航到公众号主页
            account_page_url = self.navigate_to_account_page(article_url)

            if not account_page_url:
                print("❌ 无法导航到公众号主页")
                return []

            # 2. 提取所有文章链接
            article_links = self.extract_article_links_from_page()

            if not article_links:
                print("❌ 未找到任何文章链接")
                return []

            # 3. 限制爬取数量
            links_to_crawl = article_links[:max_articles]
            print(f"📋 准备爬取 {len(links_to_crawl)} 篇文章")

            # 4. 逐个爬取文章
            for i, link in enumerate(links_to_crawl, 1):
                if link in self.crawled_urls:
                    continue

                print(f"\n📊 进度: {i}/{len(links_to_crawl)}")

                article = self.crawl_article_content(link)

                if article:
                    self.articles.append(article)
                    self.crawled_urls.add(link)

                    # 保存文章
                    saved_file = self.save_article(article)
                    if saved_file:
                        saved_files.append(saved_file)

                # 添加延迟避免被封
                time.sleep(2)

            # 5. 生成总结
            self.generate_summary(saved_files)

            print(f"\n🎉 自动爬取完成！")
            print(f"📊 成功爬取: {len(saved_files)} 篇文章")
            print(f"📁 保存目录: {self.output_dir}")

            return saved_files

        except Exception as e:
            print(f"❌ 自动爬取过程出错: {e}")
            return saved_files

        finally:
            if self.driver:
                self.driver.quit()
                print("🔒 浏览器已关闭")

    def generate_summary(self, saved_files: List[str]):
        """生成爬取总结"""
        try:
            summary_content = f"""# 自动化微信文章爬取总结

**爬取时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**总文章数:** {len(self.articles)}
**保存目录:** {self.output_dir}

## 文章列表

"""

            for i, article in enumerate(self.articles, 1):
                summary_content += f"{i}. **{article['title']}**\n"
                summary_content += f"   - 作者: {article['author']}\n"
                summary_content += f"   - 发布时间: {article['publish_time']}\n"
                summary_content += f"   - 链接: {article['url']}\n\n"

            summary_content += f"""
## 保存的文件

"""

            for i, filepath in enumerate(saved_files, 1):
                filename = Path(filepath).name
                summary_content += f"{i}. {filename}\n"

            # 保存总结文件
            summary_path = self.output_dir / f"爬取总结_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"

            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary_content)

            print(f"📋 总结文件已保存: {summary_path.name}")

        except Exception as e:
            print(f"❌ 生成总结失败: {e}")

    def interactive_crawl(self):
        """交互式爬取模式"""
        print("=== 自动化微信公众号文章爬虫 ===")
        print("请提供一篇微信公众号文章的URL，程序将自动:")
        print("1. 跳转到该公众号主页")
        print("2. 提取所有文章链接")
        print("3. 批量爬取文章内容")
        print("4. 保存为Markdown格式")
        print()

        article_url = input("请输入微信文章URL: ").strip()

        if not article_url:
            print("❌ URL不能为空")
            return

        if 'mp.weixin.qq.com' not in article_url:
            print("❌ 请输入有效的微信公众号文章URL")
            return

        max_articles = input("请输入要爬取的文章数量 (默认50): ").strip()
        try:
            max_articles = int(max_articles) if max_articles else 50
        except ValueError:
            max_articles = 50

        headless = input("是否使用无头模式? (y/N): ").strip().lower() == 'y'
        self.headless = headless

        print(f"\n🚀 开始自动爬取...")
        print(f"📄 起始文章: {article_url}")
        print(f"🎯 目标数量: {max_articles}")
        print(f"🖥️  浏览器模式: {'无头模式' if headless else '可视模式'}")
        print("-" * 60)

        saved_files = self.auto_crawl_from_article(article_url, max_articles)

        if saved_files:
            print(f"\n✅ 爬取成功完成!")
            print(f"📁 文件保存在: {self.output_dir}")
            print(f"📊 共保存 {len(saved_files)} 篇文章")
        else:
            print(f"\n❌ 爬取失败或未找到文章")


def main():
    """主函数"""
    crawler = AutoWeChatCrawler()
    crawler.interactive_crawl()


if __name__ == "__main__":
    main()
