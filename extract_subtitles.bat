@echo off
chcp 65001 >nul
echo ========================================
echo 视频字幕提取器 (支持转录和翻译)
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查FFmpeg是否安装
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo 警告: 未找到FFmpeg，程序可能无法正常工作
    echo 请运行 install_dependencies.bat 安装依赖
    echo.
)

REM 检查vidoin目录
if not exist "vidoin" (
    echo 创建vidoin目录...
    mkdir vidoin
    echo 请将视频文件放入vidoin目录后重新运行此脚本
    pause
    exit /b 0
)

REM 检查vidoin目录是否有文件
dir /b "vidoin\*.mp4" "vidoin\*.avi" "vidoin\*.mkv" "vidoin\*.mov" "vidoin\*.wmv" "vidoin\*.flv" >nul 2>&1
if errorlevel 1 (
    echo vidoin目录中未找到视频文件
    echo 请将视频文件放入vidoin目录后重新运行此脚本
    pause
    exit /b 0
)

echo 请选择处理模式:
echo 1. 仅提取现有字幕
echo 2. 启用转录功能（无字幕视频）
echo 3. 启用转录+翻译功能
echo 4. 自定义参数
echo.
set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" (
    echo 开始提取现有字幕...
    python video_subtitle_extractor.py --dir vidoin --verbose
) else if "%choice%"=="2" (
    echo 开始提取字幕并转录无字幕视频...
    python video_subtitle_extractor.py --dir vidoin --verbose --enable-transcribe
) else if "%choice%"=="3" (
    set /p api_key="请输入GLM-4-Flash API密钥: "
    echo 开始提取字幕、转录并翻译...
    python video_subtitle_extractor.py --dir vidoin --verbose --enable-transcribe --translate --openai-api-key "%api_key%"
) else if "%choice%"=="4" (
    echo 自定义参数模式
    echo 可用参数:
    echo --enable-transcribe : 启用转录
    echo --translate : 启用翻译
    echo --openai-api-key "key" : GLM API密钥
    echo --transcribe-language "zh" : 转录语言
    echo --process-type clean : 清理字幕
    echo --keep-srt : 保留SRT文件
    echo.
    set /p custom_args="请输入自定义参数: "
    python video_subtitle_extractor.py --dir vidoin --verbose %custom_args%
) else (
    echo 无效选择，使用默认模式
    python video_subtitle_extractor.py --dir vidoin --verbose
)

echo.
echo 处理完成！
echo 提取的字幕文件(.dm格式)已保存在vidoin目录中
echo.
pause
