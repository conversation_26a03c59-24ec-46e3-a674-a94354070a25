@echo off
chcp 65001 >nul
echo ========================================
echo 视频字幕提取器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查FFmpeg是否安装
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo 警告: 未找到FFmpeg，程序可能无法正常工作
    echo 请从 https://ffmpeg.org/download.html 下载并安装FFmpeg
    echo.
)

REM 检查vidoin目录
if not exist "vidoin" (
    echo 创建vidoin目录...
    mkdir vidoin
    echo 请将视频文件放入vidoin目录后重新运行此脚本
    pause
    exit /b 0
)

REM 检查vidoin目录是否有文件
dir /b "vidoin\*.mp4" "vidoin\*.avi" "vidoin\*.mkv" "vidoin\*.mov" "vidoin\*.wmv" "vidoin\*.flv" >nul 2>&1
if errorlevel 1 (
    echo vidoin目录中未找到视频文件
    echo 请将视频文件放入vidoin目录后重新运行此脚本
    pause
    exit /b 0
)

echo 开始提取字幕...
echo.

REM 运行字幕提取器
python video_subtitle_extractor.py --dir vidoin --verbose

echo.
echo 处理完成！
echo 提取的字幕文件(.dm格式)已保存在vidoin目录中
echo.
pause
