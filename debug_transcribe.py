#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版本的转录脚本
"""

import subprocess
import json
import tempfile
from pathlib import Path
import logging
import sys

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_ffmpeg():
    """检查FFmpeg是否可用"""
    try:
        logger.info("检查FFmpeg...")
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            logger.info("✓ FFmpeg可用")
            return True
        else:
            logger.error(f"FFmpeg返回错误: {result.returncode}")
            return False
    except Exception as e:
        logger.error(f"FFmpeg检查失败: {e}")
        return False

def check_ffprobe():
    """检查FFprobe是否可用"""
    try:
        logger.info("检查FFprobe...")
        result = subprocess.run(['ffprobe', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            logger.info("✓ FFprobe可用")
            return True
        else:
            logger.error(f"FFprobe返回错误: {result.returncode}")
            return False
    except Exception as e:
        logger.error(f"FFprobe检查失败: {e}")
        return False

def get_video_info(video_path):
    """获取视频信息"""
    try:
        logger.info(f"获取视频信息: {video_path}")
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', str(video_path)
        ]
        
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=30,
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            data = json.loads(result.stdout)
            logger.info("✓ 视频信息获取成功")
            
            # 显示基本信息
            format_info = data.get('format', {})
            duration = float(format_info.get('duration', 0))
            size = int(format_info.get('size', 0))
            
            logger.info(f"  时长: {duration:.2f}秒")
            logger.info(f"  大小: {size} 字节")
            
            # 显示流信息
            streams = data.get('streams', [])
            for i, stream in enumerate(streams):
                codec_type = stream.get('codec_type', 'unknown')
                codec_name = stream.get('codec_name', 'unknown')
                logger.info(f"  流{i}: {codec_type} ({codec_name})")
            
            return data
        else:
            logger.error(f"FFprobe错误: {result.stderr}")
            return None
            
    except Exception as e:
        logger.error(f"获取视频信息失败: {e}")
        return None

def extract_audio_debug(video_path, audio_path):
    """调试版本的音频提取"""
    try:
        logger.info(f"开始提取音频...")
        logger.info(f"  输入: {video_path}")
        logger.info(f"  输出: {audio_path}")
        
        cmd = [
            'ffmpeg', '-i', str(video_path),
            '-vn', '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1',
            '-y', str(audio_path)
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=300,
            encoding='utf-8',
            errors='ignore'
        )
        
        logger.info(f"FFmpeg返回码: {result.returncode}")
        
        if result.stdout:
            logger.info(f"FFmpeg输出: {result.stdout[:500]}")
        if result.stderr:
            logger.info(f"FFmpeg错误: {result.stderr[:500]}")
        
        # 检查输出文件
        if audio_path.exists():
            size = audio_path.stat().st_size
            logger.info(f"✓ 音频文件已生成，大小: {size} 字节")
            return result.returncode == 0 and size > 0
        else:
            logger.error("✗ 音频文件未生成")
            return False
        
    except subprocess.TimeoutExpired:
        logger.error("音频提取超时")
        return False
    except Exception as e:
        logger.error(f"音频提取失败: {e}")
        return False

def create_simple_srt(duration, output_path):
    """创建简单的SRT文件"""
    try:
        logger.info(f"创建SRT文件: {output_path}")
        logger.info(f"视频时长: {duration:.2f}秒")
        
        # 创建简单的字幕内容
        srt_content = f"""1
00:00:00,000 --> 00:00:10,000
[开始] - 请添加前10秒的字幕内容

2
00:00:10,000 --> 00:00:20,000
[10-20秒] - 请添加这段时间的字幕内容

3
00:00:20,000 --> 00:00:30,000
[20-30秒] - 请添加这段时间的字幕内容

"""
        
        # 如果视频较长，添加更多段落
        if duration > 30:
            segment_count = int(duration // 10) + 1
            additional_segments = []
            
            for i in range(4, min(segment_count + 1, 11)):  # 最多10段
                start_time = (i - 1) * 10
                end_time = min(i * 10, duration)
                
                start_srt = f"{start_time//3600:02d}:{(start_time%3600)//60:02d}:{start_time%60:02d},000"
                end_srt = f"{int(end_time)//3600:02d}:{(int(end_time)%3600)//60:02d}:{int(end_time)%60:02d},000"
                
                additional_segments.append(f"""{i}
{start_srt} --> {end_srt}
[{start_time}-{int(end_time)}秒] - 请添加这段时间的字幕内容

""")
            
            srt_content += "".join(additional_segments)
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(srt_content)
        
        # 验证文件
        if output_path.exists():
            size = output_path.stat().st_size
            logger.info(f"✓ SRT文件已创建，大小: {size} 字节")
            
            # 显示内容预览
            with open(output_path, 'r', encoding='utf-8') as f:
                content = f.read()
                logger.info(f"SRT内容预览:\n{content[:200]}...")
            
            return True
        else:
            logger.error("✗ SRT文件创建失败")
            return False
        
    except Exception as e:
        logger.error(f"创建SRT文件失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=== 调试版本转录脚本 ===")
    
    # 检查工具
    if not check_ffmpeg():
        logger.error("FFmpeg不可用，退出")
        return
    
    if not check_ffprobe():
        logger.error("FFprobe不可用，退出")
        return
    
    # 查找视频文件
    video_dir = Path("vidoin")
    logger.info(f"搜索视频目录: {video_dir.absolute()}")
    
    if not video_dir.exists():
        logger.error("vidoin目录不存在")
        return
    
    video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.m4v', '.webm'}
    video_files = []
    
    for ext in video_extensions:
        found_files = list(video_dir.glob(f"*{ext}"))
        video_files.extend(found_files)
        logger.info(f"找到 {len(found_files)} 个 {ext} 文件")
    
    if not video_files:
        logger.error("未找到视频文件")
        return
    
    logger.info(f"总共找到 {len(video_files)} 个视频文件")
    
    # 处理第一个视频文件
    video_file = video_files[0]
    logger.info(f"处理视频: {video_file}")
    
    # 获取视频信息
    video_info = get_video_info(video_file)
    if not video_info:
        logger.error("无法获取视频信息")
        return
    
    duration = float(video_info.get('format', {}).get('duration', 0))
    if duration <= 0:
        logger.error("视频时长无效")
        return
    
    # 生成输出文件路径
    base_name = video_file.stem
    srt_path = video_file.parent / f"{base_name}_debug.srt"
    
    logger.info(f"输出SRT路径: {srt_path}")
    
    # 创建字幕文件
    if create_simple_srt(duration, srt_path):
        logger.info("✓ 处理完成")
        print(f"\n成功创建字幕模板: {srt_path}")
        print("请手动编辑SRT文件添加字幕内容")
    else:
        logger.error("✗ 处理失败")

if __name__ == "__main__":
    main()
