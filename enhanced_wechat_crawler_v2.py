#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版微信公众号文章爬虫 V2
专门解决微信反爬机制和URL获取限制问题
"""

import requests
import re
import os
import time
import json
import random
from datetime import datetime
from typing import List, Dict, Set
from urllib.parse import quote, unquote, urlparse, parse_qs
from bs4 import BeautifulSoup
import hashlib
from pathlib import Path

class EnhancedWeChatCrawlerV2:
    def __init__(self, output_dir: str = "C:/Users/<USER>/Documents/Obsidian Vault/微信好文"):
        """
        增强版微信爬虫 V2

        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.crawled_urls: Set[str] = set()
        self.articles: List[Dict] = []
        self.account_info = {}

        # 多种User-Agent轮换
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/119.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36'
        ]

        # 创建会话
        self.session = requests.Session()
        self._setup_session()

        # 延迟配置
        self.min_delay = 3
        self.max_delay = 8

        # 已发现的URL池
        self.url_pool: Set[str] = set()

    def _setup_session(self):
        """设置会话参数"""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1'
        })

        # 设置代理（如果需要）
        # self.session.proxies = {'http': 'http://proxy:port', 'https': 'https://proxy:port'}

    def _get_random_headers(self) -> dict:
        """获取随机请求头"""
        return {
            'User-Agent': random.choice(self.user_agents),
            'Referer': 'https://mp.weixin.qq.com/',
            'X-Requested-With': 'XMLHttpRequest' if random.random() > 0.5 else None
        }

    def _random_delay(self):
        """随机延迟"""
        delay = random.uniform(self.min_delay, self.max_delay)
        print(f"⏱️  等待 {delay:.1f} 秒...")
        time.sleep(delay)

    def extract_biz_from_url(self, url: str) -> str:
        """从URL中提取biz参数"""
        try:
            if '__biz=' in url:
                biz_match = re.search(r'__biz=([^&]+)', url)
                if biz_match:
                    return unquote(biz_match.group(1))
        except:
            pass
        return None

    def search_articles_by_multiple_engines(self, account_name: str, biz: str = None, max_results: int = 50) -> List[str]:
        """使用多个搜索引擎搜索文章"""
        all_urls = set()

        search_engines = [
            self._search_baidu,
            self._search_sogou_weixin,
            self._search_bing,
            self._search_360,
        ]

        for engine in search_engines:
            try:
                print(f"🔍 使用 {engine.__name__} 搜索...")
                urls = engine(account_name, biz, max_results // len(search_engines))
                all_urls.update(urls)

                if len(all_urls) >= max_results:
                    break

                self._random_delay()

            except Exception as e:
                print(f"❌ {engine.__name__} 搜索失败: {e}")
                continue

        return list(all_urls)[:max_results]

    def _search_baidu(self, account_name: str, biz: str = None, max_results: int = 20) -> List[str]:
        """百度搜索"""
        urls = set()

        queries = [
            f'site:mp.weixin.qq.com "{account_name}"',
            f'site:mp.weixin.qq.com "{account_name}" 管理',
            f'site:mp.weixin.qq.com "{account_name}" 团队',
            f'site:mp.weixin.qq.com "{account_name}" 领导',
        ]

        if biz:
            queries.append(f'site:mp.weixin.qq.com __biz={biz}')

        for query in queries[:3]:  # 限制查询数量
            try:
                search_url = f"https://www.baidu.com/s?wd={quote(query)}&pn=0"

                headers = self._get_random_headers()
                response = self.session.get(search_url, headers=headers, timeout=15)
                response.raise_for_status()

                soup = BeautifulSoup(response.text, 'html.parser')

                # 提取搜索结果链接
                for link in soup.find_all('a', href=True):
                    href = link.get('href', '')
                    if 'mp.weixin.qq.com/s' in href:
                        clean_url = self._extract_real_url(href)
                        if clean_url and self._validate_wechat_url(clean_url):
                            urls.add(clean_url)
                            if len(urls) >= max_results:
                                break

                time.sleep(2)

            except Exception as e:
                print(f"百度搜索查询失败 {query}: {e}")
                continue

        return list(urls)

    def _search_sogou_weixin(self, account_name: str, biz: str = None, max_results: int = 20) -> List[str]:
        """搜狗微信搜索"""
        urls = set()

        try:
            # 搜狗微信搜索
            search_url = f"https://weixin.sogou.com/weixin?type=1&query={quote(account_name)}"

            headers = self._get_random_headers()
            headers['Referer'] = 'https://weixin.sogou.com/'

            response = self.session.get(search_url, headers=headers, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找文章链接
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if 'mp.weixin.qq.com' in href:
                    clean_url = self._extract_real_url(href)
                    if clean_url and self._validate_wechat_url(clean_url):
                        urls.add(clean_url)
                        if len(urls) >= max_results:
                            break

        except Exception as e:
            print(f"搜狗微信搜索失败: {e}")

        return list(urls)

    def _search_bing(self, account_name: str, biz: str = None, max_results: int = 20) -> List[str]:
        """必应搜索"""
        urls = set()

        try:
            query = f'site:mp.weixin.qq.com "{account_name}"'
            search_url = f"https://www.bing.com/search?q={quote(query)}"

            headers = self._get_random_headers()
            response = self.session.get(search_url, headers=headers, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if 'mp.weixin.qq.com/s' in href:
                    clean_url = self._extract_real_url(href)
                    if clean_url and self._validate_wechat_url(clean_url):
                        urls.add(clean_url)
                        if len(urls) >= max_results:
                            break

        except Exception as e:
            print(f"必应搜索失败: {e}")

        return list(urls)

    def _search_360(self, account_name: str, biz: str = None, max_results: int = 20) -> List[str]:
        """360搜索"""
        urls = set()

        try:
            query = f'site:mp.weixin.qq.com "{account_name}"'
            search_url = f"https://www.so.com/s?q={quote(query)}"

            headers = self._get_random_headers()
            response = self.session.get(search_url, headers=headers, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if 'mp.weixin.qq.com/s' in href:
                    clean_url = self._extract_real_url(href)
                    if clean_url and self._validate_wechat_url(clean_url):
                        urls.add(clean_url)
                        if len(urls) >= max_results:
                            break

        except Exception as e:
            print(f"360搜索失败: {e}")

        return list(urls)

    def _extract_real_url(self, url: str) -> str:
        """从搜索结果中提取真实URL"""
        if not url:
            return ""

        # 处理百度重定向
        if 'baidu.com' in url and 'url=' in url:
            try:
                parsed = urlparse(url)
                params = parse_qs(parsed.query)
                if 'url' in params:
                    return params['url'][0]
            except:
                pass

        # 处理其他重定向
        if url.startswith('http') and 'mp.weixin.qq.com' in url:
            return url

        return ""

    def _validate_wechat_url(self, url: str) -> bool:
        """验证微信URL有效性"""
        if not url:
            return False

        # 基本格式检查
        if not url.startswith(('http://', 'https://')):
            return False

        if 'mp.weixin.qq.com/s' not in url:
            return False

        # 检查是否包含必要参数
        if '/s/' in url or ('__biz=' in url and 'mid=' in url):
            return True

        return False

    def discover_urls_from_content(self, content: str) -> List[str]:
        """从内容中发现更多URL"""
        urls = set()

        # 多种URL模式
        patterns = [
            r'https://mp\.weixin\.qq\.com/s/[A-Za-z0-9_-]+',
            r'https://mp\.weixin\.qq\.com/s\?[^"\s<>]+',
            r'http://mp\.weixin\.qq\.com/s/[A-Za-z0-9_-]+',
            r'http://mp\.weixin\.qq\.com/s\?[^"\s<>]+',
        ]

        for pattern in patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                clean_url = self._extract_real_url(match)
                if self._validate_wechat_url(clean_url):
                    urls.add(clean_url)

        return list(urls)

    def crawl_single_article_enhanced(self, url: str) -> Dict:
        """增强版单篇文章爬取"""
        try:
            print(f"📄 爬取文章: {url}")

            headers = self._get_random_headers()
            headers['Referer'] = 'https://mp.weixin.qq.com/'

            response = self.session.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'

            # 检查是否被重定向到错误页面
            if any(keyword in response.text for keyword in ['该内容已被发布者删除', '此内容因违规无法查看', '链接已过期']):
                print(f"⚠️  文章可能已被删除或限制访问: {url}")
                return None

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取账号信息（如果是第一篇）
            if not self.account_info:
                self.account_info = self._extract_account_info(soup, response.text)
                if self.account_info.get('name'):
                    print(f"📱 检测到公众号: {self.account_info['name']}")

            # 提取文章信息
            article_info = self._extract_article_info(soup, url)

            if article_info:
                # 从文章内容中发现更多URL
                discovered_urls = self.discover_urls_from_content(response.text)
                self.url_pool.update(discovered_urls)

                print(f"✅ 成功爬取: {article_info['title']}")
                print(f"🔍 发现 {len(discovered_urls)} 个新URL")

                return article_info

            return None

        except Exception as e:
            print(f"❌ 爬取文章失败 {url}: {e}")
            return None

    def _extract_account_info(self, soup, html_content: str) -> Dict:
        """提取账号信息"""
        account_info = {}

        # 提取公众号名称
        selectors = ['#js_name', '.profile_nickname', '.rich_media_meta_text', '.account_nickname']

        for selector in selectors:
            elem = soup.select_one(selector)
            if elem:
                name = elem.get_text().strip()
                if name and len(name) > 1:
                    account_info['name'] = name
                    break

        # 提取biz参数
        biz_match = re.search(r'__biz=([^&"\']+)', html_content)
        if biz_match:
            account_info['biz'] = biz_match.group(1)

        return account_info

    def _extract_article_info(self, soup, url: str) -> Dict:
        """提取文章信息"""
        try:
            # 提取标题
            title_selectors = ['h1.rich_media_title', 'h1#activity-name', 'h1', '.rich_media_title']
            title = "未知标题"

            for selector in title_selectors:
                elem = soup.select_one(selector)
                if elem:
                    title = elem.get_text().strip()
                    if title and len(title) > 3:
                        break

            # 提取作者
            author_selectors = ['.rich_media_meta_text', '.profile_nickname', '#js_name']
            author = self.account_info.get('name', '未知作者')

            for selector in author_selectors:
                elem = soup.select_one(selector)
                if elem:
                    author_text = elem.get_text().strip()
                    if author_text:
                        author = author_text
                        break

            # 提取发布时间
            publish_time = self._extract_publish_time(soup)

            # 提取内容
            content = self._extract_content(soup)

            return {
                'title': title,
                'author': author,
                'publish_time': publish_time,
                'content': content,
                'url': url,
                'crawl_time': datetime.now().isoformat()
            }

        except Exception as e:
            print(f"❌ 提取文章信息失败: {e}")
            return None

    def _extract_publish_time(self, soup) -> str:
        """提取发布时间"""
        time_selectors = ['#publish_time', '.rich_media_meta_text', '[id*="time"]']

        for selector in time_selectors:
            elem = soup.select_one(selector)
            if elem:
                time_text = elem.get_text().strip()
                if re.search(r'\d{4}[-年]\d{1,2}[-月]\d{1,2}', time_text):
                    return time_text

        return datetime.now().strftime('%Y-%m-%d')

    def _extract_content(self, soup) -> str:
        """提取文章内容"""
        content_selectors = ['#js_content', '.rich_media_content', '.content']

        for selector in content_selectors:
            elem = soup.select_one(selector)
            if elem:
                # 移除脚本和样式
                for script in elem(["script", "style"]):
                    script.decompose()

                text = elem.get_text()
                lines = (line.strip() for line in text.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                content = '\n'.join(chunk for chunk in chunks if chunk)

                return content

        return "无法提取内容"

    def save_article(self, article: Dict) -> str:
        """保存文章"""
        try:
            safe_title = re.sub(r'[<>:"/\\|?*]', '_', article['title'])
            safe_title = safe_title[:100]

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{safe_title}_{timestamp}.md"
            filepath = self.output_dir / filename

            counter = 1
            while filepath.exists():
                filename = f"{safe_title}_{timestamp}_{counter}.md"
                filepath = self.output_dir / filename
                counter += 1

            markdown_content = f"""# {article['title']}

**作者:** {article['author']}
**发布时间:** {article['publish_time']}
**原文链接:** {article['url']}
**爬取时间:** {article['crawl_time']}

---

{article['content']}

---

*本文由增强版微信爬虫V2抓取*
"""

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            print(f"💾 文章已保存: {filename}")
            return str(filepath)

        except Exception as e:
            print(f"❌ 保存文章失败: {e}")
            return None

    def crawl_articles_enhanced(self, start_url: str, max_articles: int = 100) -> List[str]:
        """增强版文章爬取主函数"""
        print(f"🚀 启动增强版微信爬虫V2")
        print(f"📄 起始URL: {start_url}")
        print(f"🎯 目标文章数: {max_articles}")
        print("-" * 80)

        saved_files = []
        urls_to_crawl = [start_url]

        # 第一阶段：爬取起始文章并提取账号信息
        print("📍 第一阶段：分析起始文章...")
        first_article = self.crawl_single_article_enhanced(start_url)

        if first_article:
            self.articles.append(first_article)
            self.crawled_urls.add(start_url)

            saved_file = self.save_article(first_article)
            if saved_file:
                saved_files.append(saved_file)

        # 第二阶段：多引擎搜索相关文章
        if self.account_info.get('name'):
            account_name = self.account_info['name']
            biz = self.account_info.get('biz')

            print(f"\n🔍 第二阶段：多引擎搜索 '{account_name}' 的文章...")

            search_urls = self.search_articles_by_multiple_engines(
                account_name, biz, max_articles * 2
            )

            print(f"🎯 搜索发现 {len(search_urls)} 个潜在文章URL")

            # 合并URL池
            all_urls = set(search_urls)
            all_urls.update(self.url_pool)
            all_urls.discard(start_url)  # 移除起始URL

            urls_to_crawl.extend(list(all_urls))

        # 第三阶段：批量爬取文章
        print(f"\n📚 第三阶段：批量爬取文章...")
        print(f"📋 待爬取URL总数: {len(urls_to_crawl)}")

        for i, url in enumerate(urls_to_crawl):
            if len(self.articles) >= max_articles:
                print(f"🎯 已达到目标文章数量: {max_articles}")
                break

            if url in self.crawled_urls:
                continue

            print(f"\n📊 进度: {len(self.articles)}/{max_articles} | 处理第 {i+1}/{len(urls_to_crawl)} 个URL")

            article = self.crawl_single_article_enhanced(url)

            if article:
                # 验证文章是否属于目标账号
                if self._is_target_account_article(article):
                    self.articles.append(article)
                    self.crawled_urls.add(url)

                    saved_file = self.save_article(article)
                    if saved_file:
                        saved_files.append(saved_file)
                else:
                    print(f"⚠️  文章不属于目标账号，跳过")

            # 随机延迟
            self._random_delay()

        # 生成总结
        self.generate_summary(saved_files)

        print(f"\n🎉 爬取完成！")
        print(f"📊 成功爬取: {len(self.articles)} 篇文章")
        print(f"📁 保存目录: {self.output_dir}")

        return saved_files

    def _is_target_account_article(self, article: Dict) -> bool:
        """判断文章是否属于目标账号"""
        if not self.account_info.get('name'):
            return True

        target_name = self.account_info['name']
        article_author = article.get('author', '')

        # 简单的名称匹配
        if target_name in article_author or article_author in target_name:
            return True

        # 检查文章URL中的biz参数
        if self.account_info.get('biz'):
            target_biz = self.account_info['biz']
            article_url = article.get('url', '')
            if target_biz in article_url:
                return True

        return True  # 默认接受，避免过度过滤

    def generate_summary(self, saved_files: List[str]):
        """生成爬取总结"""
        try:
            account_name = self.account_info.get('name', '未知公众号')

            summary_content = f"""# 增强版微信爬虫V2 - 爬取总结

**公众号:** {account_name}
**爬取时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**总文章数:** {len(self.articles)}
**保存目录:** {self.output_dir}

## 账号信息
- **名称:** {account_name}
- **BIZ参数:** {self.account_info.get('biz', '未获取')}

## 爬取策略
- ✅ 多搜索引擎发现
- ✅ 内容URL提取
- ✅ 智能去重过滤
- ✅ 随机延迟防封

## 文章列表

"""

            for i, article in enumerate(self.articles, 1):
                summary_content += f"{i}. **{article['title']}**\n"
                summary_content += f"   - 作者: {article['author']}\n"
                summary_content += f"   - 发布时间: {article['publish_time']}\n"
                summary_content += f"   - 链接: {article['url']}\n\n"

            summary_content += f"""
## 保存的文件

"""

            for i, filepath in enumerate(saved_files, 1):
                filename = Path(filepath).name
                summary_content += f"{i}. {filename}\n"

            summary_content += f"""

## 技术统计
- **URL池大小:** {len(self.url_pool)}
- **已爬取URL:** {len(self.crawled_urls)}
- **成功率:** {len(self.articles)/len(self.crawled_urls)*100:.1f}%

---
*由增强版微信爬虫V2生成*
"""

            summary_path = self.output_dir / f"爬取总结_V2_{account_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"

            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary_content)

            print(f"📋 总结文件已保存: {summary_path.name}")

        except Exception as e:
            print(f"❌ 生成总结失败: {e}")


def main():
    """主函数"""
    print("=== 增强版微信公众号文章爬虫 V2 ===")
    print("专门解决微信反爬机制和URL获取限制问题")
    print()

    # 获取用户输入
    start_url = input("请输入微信文章URL: ").strip()

    if not start_url:
        print("❌ URL不能为空")
        return

    if 'mp.weixin.qq.com' not in start_url:
        print("❌ 请输入有效的微信公众号文章URL")
        return

    max_articles = input("请输入要爬取的文章数量 (默认100): ").strip()
    try:
        max_articles = int(max_articles) if max_articles else 100
    except ValueError:
        max_articles = 100

    output_dir = input("请输入输出目录 (默认: C:/Users/<USER>/Documents/Obsidian Vault/微信好文): ").strip()
    if not output_dir:
        output_dir = "C:/Users/<USER>/Documents/Obsidian Vault/微信好文"

    # 创建爬虫实例
    crawler = EnhancedWeChatCrawlerV2(output_dir=output_dir)

    try:
        # 开始爬取
        saved_files = crawler.crawl_articles_enhanced(start_url, max_articles)

        if saved_files:
            print(f"\n✅ 爬取成功完成!")
            print(f"📁 文件保存在: {output_dir}")
            print(f"📊 共保存 {len(saved_files)} 篇文章")
        else:
            print(f"\n❌ 爬取失败或未找到文章")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断了爬取过程")
    except Exception as e:
        print(f"\n❌ 爬取过程出错: {e}")


if __name__ == "__main__":
    main()