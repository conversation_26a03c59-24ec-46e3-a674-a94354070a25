@echo off
chcp 65001 >nul
echo ========================================
echo 视频字幕提取器 - 依赖安装
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo 正在安装Python依赖包...
echo.

REM 安装基础依赖
echo 安装基础依赖...
pip install requests pathlib argparse

REM 安装Whisper（用于转录）
echo.
echo 安装Whisper转录依赖...
pip install openai-whisper

REM 安装音频处理依赖
echo.
echo 安装音频处理依赖...
pip install torch torchaudio

REM 安装其他可能需要的依赖
echo.
echo 安装其他依赖...
pip install numpy scipy

echo.
echo ========================================
echo 检查FFmpeg安装状态
echo ========================================

REM 检查FFmpeg是否安装
ffmpeg -version >nul 2>&1
if errorlevel 1 (
    echo.
    echo 警告: 未找到FFmpeg
    echo FFmpeg是必需的依赖，用于视频和音频处理
    echo.
    echo 请按照以下步骤安装FFmpeg:
    echo 1. 访问 https://ffmpeg.org/download.html
    echo 2. 下载Windows版本
    echo 3. 解压到任意目录（如 C:\ffmpeg）
    echo 4. 将 C:\ffmpeg\bin 添加到系统PATH环境变量
    echo 5. 重启命令提示符
    echo.
    echo 或者使用Chocolatey安装: choco install ffmpeg
    echo 或者使用winget安装: winget install ffmpeg
    echo.
) else (
    echo FFmpeg已安装并可用
    ffmpeg -version | findstr "ffmpeg version"
)

echo.
echo ========================================
echo 安装完成
echo ========================================
echo.
echo 依赖安装完成！
echo.
echo 使用方法:
echo 1. 基本字幕提取: python video_subtitle_extractor.py
echo 2. 启用转录功能: python video_subtitle_extractor.py --enable-transcribe
echo 3. 启用翻译功能: python video_subtitle_extractor.py --translate --openai-api-key "your_key"
echo 4. 完整功能: python video_subtitle_extractor.py --enable-transcribe --translate --openai-api-key "your_key"
echo.
pause
