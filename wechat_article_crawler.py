#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信公众号文章爬虫
功能：
1. 爬取指定的微信公众号文章
2. 深入爬取文章目录下的其他45篇文章
3. 将所有文章保存为Markdown格式
"""

import asyncio
import os
import re
import json
import time
from datetime import datetime
from typing import List, Dict, Set
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

# 导入crawl4ai相关模块
import sys
sys.path.append('./crawl4ai')

from crawl4ai import AsyncWebCrawler, CacheMode, BrowserConfig, CrawlerRunConfig
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from crawl4ai.content_filter_strategy import PruningContentFilter
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

class WeChatArticleCrawler:
    def __init__(self, output_dir: str = "E:\mcp-test\obsidian wang\微信好文"):
        """
        初始化微信文章爬虫
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        self.crawled_urls: Set[str] = set()
        self.articles: List[Dict] = []
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 浏览器配置
        self.browser_config = BrowserConfig(
            headless=True,
            java_script_enabled=True,
            user_agent_mode="random",
            user_agent_generator_config={"device_type": "mobile", "os_type": "android"}
        )
        
        # 爬虫配置
        self.crawler_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            excluded_tags=["nav", "footer", "aside", "script", "style"],
            remove_overlay_elements=True,
            markdown_generator=DefaultMarkdownGenerator(
                content_filter=PruningContentFilter(
                    threshold=0.48, 
                    threshold_type="fixed", 
                    min_word_threshold=10
                ),
                options={"ignore_links": False}
            ),
            page_timeout=30000,
            delay_before_return_html=2
        )

    def clean_filename(self, filename: str) -> str:
        """清理文件名，移除非法字符"""
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(filename) > 100:
            filename = filename[:100]
        return filename.strip()

    def extract_article_links(self, html_content: str, base_url: str) -> List[str]:
        """
        从HTML内容中提取文章链接
        
        Args:
            html_content: HTML内容
            base_url: 基础URL
            
        Returns:
            文章链接列表
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        links = []
        
        # 查找微信文章链接的常见模式
        link_patterns = [
            'a[href*="mp.weixin.qq.com/s"]',  # 微信文章链接
            'a[href*="mp.weixin.qq.com/s?"]', # 带参数的微信文章链接
        ]
        
        for pattern in link_patterns:
            found_links = soup.select(pattern)
            for link in found_links:
                href = link.get('href')
                if href:
                    # 处理相对链接
                    full_url = urljoin(base_url, href)
                    if full_url not in self.crawled_urls:
                        links.append(full_url)
                        
        # 也尝试从文本中提取链接
        text_content = soup.get_text()
        url_pattern = r'https?://mp\.weixin\.qq\.com/s[^\s\)]*'
        text_urls = re.findall(url_pattern, text_content)
        
        for url in text_urls:
            if url not in self.crawled_urls:
                links.append(url)
        
        return list(set(links))  # 去重

    async def crawl_single_article(self, url: str) -> Dict:
        """
        爬取单篇文章
        
        Args:
            url: 文章URL
            
        Returns:
            文章信息字典
        """
        print(f"正在爬取文章: {url}")
        
        async with AsyncWebCrawler(config=self.browser_config) as crawler:
            try:
                result = await crawler.arun(url=url, config=self.crawler_config)
                
                if not result.success:
                    print(f"爬取失败: {url}")
                    return None
                
                # 解析文章信息
                soup = BeautifulSoup(result.cleaned_html, 'html.parser')
                
                # 提取标题
                title = ""
                title_selectors = [
                    'h1.rich_media_title',
                    'h1#activity-name', 
                    'h1',
                    '.rich_media_title',
                    '#activity-name'
                ]
                
                for selector in title_selectors:
                    title_elem = soup.select_one(selector)
                    if title_elem:
                        title = title_elem.get_text().strip()
                        break
                
                if not title:
                    title = f"文章_{int(time.time())}"
                
                # 提取作者
                author = ""
                author_selectors = [
                    '.rich_media_meta_text',
                    '.profile_nickname',
                    '#js_name'
                ]
                
                for selector in author_selectors:
                    author_elem = soup.select_one(selector)
                    if author_elem:
                        author = author_elem.get_text().strip()
                        break
                
                # 提取发布时间
                publish_time = ""
                time_selectors = [
                    '#publish_time',
                    '.rich_media_meta_text',
                    '[id*="time"]'
                ]
                
                for selector in time_selectors:
                    time_elem = soup.select_one(selector)
                    if time_elem:
                        publish_time = time_elem.get_text().strip()
                        break
                
                article_info = {
                    'title': title,
                    'author': author,
                    'publish_time': publish_time,
                    'url': url,
                    'content': result.markdown,
                    'crawl_time': datetime.now().isoformat()
                }
                
                # 提取文章中的其他链接
                article_links = self.extract_article_links(result.cleaned_html, url)
                article_info['found_links'] = article_links
                
                self.crawled_urls.add(url)
                print(f"成功爬取: {title}")
                
                return article_info
                
            except Exception as e:
                print(f"爬取文章时出错 {url}: {str(e)}")
                return None

    async def save_article_to_markdown(self, article: Dict) -> str:
        """
        将文章保存为Markdown文件
        
        Args:
            article: 文章信息
            
        Returns:
            保存的文件路径
        """
        if not article:
            return None
            
        # 生成文件名
        title = self.clean_filename(article['title'])
        filename = f"{title}.md"
        filepath = os.path.join(self.output_dir, filename)
        
        # 避免文件名冲突
        counter = 1
        while os.path.exists(filepath):
            filename = f"{title}_{counter}.md"
            filepath = os.path.join(self.output_dir, filename)
            counter += 1
        
        # 生成Markdown内容
        markdown_content = f"""# {article['title']}

**作者:** {article['author']}  
**发布时间:** {article['publish_time']}  
**原文链接:** {article['url']}  
**爬取时间:** {article['crawl_time']}

---

{article['content']}

---

*本文由微信文章爬虫自动抓取并转换为Markdown格式*
"""
        
        # 保存文件
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            print(f"文章已保存: {filepath}")
            return filepath
        except Exception as e:
            print(f"保存文章失败 {filepath}: {str(e)}")
            return None

    async def crawl_articles(self, start_url: str, max_articles: int = 46) -> List[str]:
        """
        爬取文章（包括起始文章和相关文章）
        
        Args:
            start_url: 起始文章URL
            max_articles: 最大文章数量
            
        Returns:
            保存的文件路径列表
        """
        print(f"开始爬取微信文章，起始URL: {start_url}")
        print(f"目标文章数量: {max_articles}")
        
        saved_files = []
        urls_to_crawl = [start_url]
        
        while len(self.articles) < max_articles and urls_to_crawl:
            current_url = urls_to_crawl.pop(0)
            
            if current_url in self.crawled_urls:
                continue
                
            # 爬取当前文章
            article = await self.crawl_single_article(current_url)
            
            if article:
                self.articles.append(article)
                
                # 保存文章
                saved_file = await self.save_article_to_markdown(article)
                if saved_file:
                    saved_files.append(saved_file)
                
                # 添加发现的新链接到待爬取列表
                if 'found_links' in article:
                    for link in article['found_links']:
                        if link not in self.crawled_urls and link not in urls_to_crawl:
                            urls_to_crawl.append(link)
                
                print(f"进度: {len(self.articles)}/{max_articles}")
                
                # 添加延迟避免被封
                await asyncio.sleep(2)
            
            # 如果没有更多链接，尝试搜索相关文章
            if not urls_to_crawl and len(self.articles) < max_articles:
                print("正在搜索更多相关文章...")
                # 这里可以添加更多搜索逻辑
                break
        
        print(f"爬取完成！共爬取 {len(self.articles)} 篇文章")
        return saved_files

    async def generate_summary(self, saved_files: List[str]) -> str:
        """
        生成爬取总结
        
        Args:
            saved_files: 保存的文件列表
            
        Returns:
            总结文件路径
        """
        summary_content = f"""# 微信文章爬取总结

**爬取时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**总文章数:** {len(self.articles)}  
**保存目录:** {self.output_dir}

## 文章列表

"""
        
        for i, article in enumerate(self.articles, 1):
            summary_content += f"{i}. **{article['title']}**\n"
            summary_content += f"   - 作者: {article['author']}\n"
            summary_content += f"   - 发布时间: {article['publish_time']}\n"
            summary_content += f"   - 原文链接: {article['url']}\n\n"
        
        summary_content += f"""
## 保存的文件

"""
        
        for i, filepath in enumerate(saved_files, 1):
            filename = os.path.basename(filepath)
            summary_content += f"{i}. {filename}\n"
        
        # 保存总结文件
        summary_path = os.path.join(self.output_dir, f"爬取总结_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
        
        try:
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary_content)
            print(f"总结文件已保存: {summary_path}")
            return summary_path
        except Exception as e:
            print(f"保存总结文件失败: {str(e)}")
            return None


async def main():
    """主函数"""
    # 目标URL
    start_url = "https://mp.weixin.qq.com/s/qJN0eQliHRFl2WbIt4czSg"
    
    # 创建爬虫实例
    crawler = WeChatArticleCrawler(output_dir="E:/mcp-test")
    
    try:
        # 开始爬取
        saved_files = await crawler.crawl_articles(start_url, max_articles=46)
        
        # 生成总结
        summary_file = await crawler.generate_summary(saved_files)
        
        print("\n" + "="*50)
        print("爬取任务完成！")
        print(f"共爬取文章: {len(crawler.articles)} 篇")
        print(f"保存目录: {crawler.output_dir}")
        if summary_file:
            print(f"总结文件: {summary_file}")
        print("="*50)
        
    except Exception as e:
        print(f"爬取过程中出现错误: {str(e)}")


if __name__ == "__main__":
    print("微信公众号文章爬虫启动...")
    asyncio.run(main())
