<!DOCTYPE html>
<html>
<head>
  <title>文件格式转换器</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div class="container">
    <h1>文件格式转换工具</h1>
    <div class="upload-area">
      <input type="file" id="fileInput" multiple accept=".docx,.xlsx">
      <label for="fileInput">点击或拖拽文件上传（支持.docx/.xlsx）</label>
    </div>
    <div class="progress-container">
      <div id="progressBar" class="progress-bar"></div>
    </div>
    <div class="results">
      <h2>转换结果</h2>
      <div id="resultList"></div>
    </div>
    <div class="history">
      <h2>转换历史</h2>
      <div id="historyList"></div>
    </div>
  </div>
  <script src="renderer.js"></script>
</body>
</html>