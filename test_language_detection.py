#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语言检测功能测试脚本
"""

import sys
from pathlib import Path

def test_language_detection():
    """测试语言检测功能"""
    print("=== 语言检测功能测试 ===")
    
    try:
        from video_subtitle_extractor import LanguageDetector
        detector = LanguageDetector()
        print("✓ 语言检测器初始化成功")
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return
    
    # 测试文本
    test_texts = [
        ("这是一段中文文本，用来测试语言检测功能。", "zh"),
        ("This is an English text for testing language detection.", "en"),
        ("Hello world, this is a test.", "en"),
        ("你好世界，这是一个测试。", "zh"),
        ("混合文本 mixed text 中英文", "zh"),  # 中文字符较多
        ("Mixed text 混合文本 with English", "en"),  # 英文字符较多
        ("123456789", "unknown"),  # 纯数字
        ("", "unknown"),  # 空文本
    ]
    
    print("\n测试文本语言检测:")
    for text, expected in test_texts:
        detected = detector.detect_language_from_text(text)
        status = "✓" if detected == expected else "✗"
        print(f"{status} '{text[:30]}...' -> 检测: {detected}, 期望: {expected}")
    
    # 测试翻译决策
    print("\n测试翻译决策:")
    languages = ["zh", "en", "ja", "fr", "unknown"]
    for lang in languages:
        should_translate = detector.should_translate(lang)
        print(f"语言: {lang} -> 需要翻译: {should_translate}")

def test_srt_content():
    """测试SRT内容语言检测"""
    print("\n=== SRT内容语言检测测试 ===")
    
    try:
        from video_subtitle_extractor import LanguageDetector
        detector = LanguageDetector()
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return
    
    # 模拟SRT内容
    chinese_srt = """1
00:00:01,000 --> 00:00:03,000
这是第一句中文字幕

2
00:00:04,000 --> 00:00:06,000
这是第二句中文字幕
"""
    
    english_srt = """1
00:00:01,000 --> 00:00:03,000
This is the first English subtitle

2
00:00:04,000 --> 00:00:06,000
This is the second English subtitle
"""
    
    # 创建临时SRT文件进行测试
    test_dir = Path("test_temp")
    test_dir.mkdir(exist_ok=True)
    
    try:
        from video_subtitle_extractor import VideoSubtitleExtractor
        extractor = VideoSubtitleExtractor()
        
        # 测试中文SRT
        chinese_srt_path = test_dir / "chinese_test.srt"
        with open(chinese_srt_path, 'w', encoding='utf-8') as f:
            f.write(chinese_srt)
        
        detected_lang = extractor.detect_subtitle_language(chinese_srt_path)
        print(f"✓ 中文SRT检测结果: {detected_lang}")
        
        # 测试英文SRT
        english_srt_path = test_dir / "english_test.srt"
        with open(english_srt_path, 'w', encoding='utf-8') as f:
            f.write(english_srt)
        
        detected_lang = extractor.detect_subtitle_language(english_srt_path)
        print(f"✓ 英文SRT检测结果: {detected_lang}")
        
    except Exception as e:
        print(f"✗ SRT测试失败: {e}")
    finally:
        # 清理测试文件
        try:
            for file in test_dir.glob("*.srt"):
                file.unlink()
            test_dir.rmdir()
        except:
            pass

def test_whisper_result():
    """测试Whisper结果语言检测"""
    print("\n=== Whisper结果语言检测测试 ===")
    
    try:
        from video_subtitle_extractor import LanguageDetector
        detector = LanguageDetector()
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return
    
    # 模拟Whisper结果
    test_results = [
        {"language": "chinese", "text": "这是中文转录结果"},
        {"language": "english", "text": "This is English transcription"},
        {"language": "zh", "text": "中文测试"},
        {"language": "en", "text": "English test"},
        {"text": "这是没有语言标记的中文文本"},
        {"text": "This is English text without language tag"},
        {}  # 空结果
    ]
    
    for i, result in enumerate(test_results):
        detected = detector.detect_language_from_whisper_result(result)
        print(f"测试 {i+1}: {result} -> 检测: {detected}")

def main():
    """主测试函数"""
    print("智能语言检测功能测试")
    print("=" * 40)
    
    test_language_detection()
    test_srt_content()
    test_whisper_result()
    
    print("\n=== 测试完成 ===")
    print("如果所有测试都通过，语言检测功能正常工作。")

if __name__ == "__main__":
    main()
