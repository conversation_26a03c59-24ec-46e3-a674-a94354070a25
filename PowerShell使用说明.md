# PowerShell环境使用说明

## 问题解决

如果您遇到 `extract_subtitles.bat` 无法识别的错误，这是因为您在PowerShell环境中，需要使用以下方法：

## 解决方案

### 方法1：使用PowerShell脚本（推荐）
```powershell
# 交互式界面
.\extract_subtitles.ps1

# 或者使用快速启动脚本
.\start.ps1
```

### 方法2：在PowerShell中执行批处理文件
```powershell
# 使用相对路径
.\extract_subtitles.bat

# 或者使用cmd命令
cmd /c extract_subtitles.bat
```

### 方法3：直接使用Python命令
```powershell
# 智能模式（推荐）
python video_subtitle_extractor.py --enable-transcribe --openai-api-key "7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0" --openai-base-url "https://open.bigmodel.cn/api/paas/v4"

# 仅提取字幕
python video_subtitle_extractor.py --disable-auto-translate

# 强制翻译
python video_subtitle_extractor.py --enable-transcribe --force-translate --openai-api-key "7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0"
```

## 快速启动选项

### 使用start.ps1快速启动
```powershell
# 智能模式（默认）
.\start.ps1

# 仅提取字幕
.\start.ps1 -Mode extract

# 仅转录
.\start.ps1 -Mode transcribe

# 强制翻译
.\start.ps1 -Mode translate

# 自定义API密钥
.\start.ps1 -Mode smart -ApiKey "your_api_key"
```

## PowerShell执行策略

如果遇到执行策略错误，请运行：
```powershell
# 临时允许执行脚本
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 或者绕过执行策略
PowerShell -ExecutionPolicy Bypass -File extract_subtitles.ps1
```

## 文件说明

- `extract_subtitles.ps1` - PowerShell版本的交互式脚本
- `start.ps1` - 快速启动脚本，支持参数
- `extract_subtitles.bat` - 批处理版本（需要在cmd中运行）
- `video_subtitle_extractor.py` - 主程序

## 使用建议

1. **推荐使用PowerShell脚本**：功能更完整，界面更友好
2. **快速启动**：使用 `.\start.ps1` 进行快速处理
3. **自定义需求**：直接使用Python命令行参数

## 常见问题

### Q: PowerShell无法执行脚本
A: 运行 `Set-ExecutionPolicy RemoteSigned -Scope CurrentUser`

### Q: 找不到Python
A: 确保Python已安装并添加到PATH环境变量

### Q: 找不到FFmpeg
A: 运行 `install_dependencies.bat` 或手动安装FFmpeg

### Q: API调用失败
A: 检查网络连接和API密钥是否正确
