#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分批微信公众号文章爬虫（支持图片下载）
在原有功能基础上添加分批爬取和跳过功能
"""

import json
import time
from pathlib import Path
from typing import List, Dict, Set
from wechat_crawler_with_images import WeChatCrawlerWithImages

class BatchWeChatCrawlerWithImages(WeChatCrawlerWithImages):
    def __init__(self, output_dir: str = "E:/mcp-test/obsidian wang/微信好文"):
        """
        分批微信爬虫（支持图片下载）
        
        Args:
            output_dir: 输出目录
        """
        super().__init__(output_dir)
        
        # 历史记录文件
        self.crawled_urls_file = self.output_dir / "crawled_urls_history.json"
        self.batch_stats_file = self.output_dir / "batch_stats.json"
        
        # 已爬取URL历史
        self.crawled_urls_history: Set[str] = set()
        
        # 批次统计
        self.batch_stats = {
            'total_batches': 0,
            'total_articles': 0,
            'total_images': 0,
            'last_batch_time': None
        }
        
        # 加载历史记录
        self.load_history()
    
    def load_history(self):
        """加载历史记录"""
        try:
            # 加载已爬取URL历史
            if self.crawled_urls_file.exists():
                with open(self.crawled_urls_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.crawled_urls_history.update(data.get('crawled_urls', []))
                    print(f"📚 加载历史记录: {len(self.crawled_urls_history)} 个已爬取URL")
            
            # 加载批次统计
            if self.batch_stats_file.exists():
                with open(self.batch_stats_file, 'r', encoding='utf-8') as f:
                    self.batch_stats.update(json.load(f))
                    print(f"📊 历史统计: 已完成 {self.batch_stats['total_batches']} 个批次")
                    
        except Exception as e:
            print(f"⚠️  加载历史记录失败: {e}")
    
    def save_history(self):
        """保存历史记录"""
        try:
            # 保存URL历史
            url_data = {
                'crawled_urls': list(self.crawled_urls_history),
                'last_update': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_count': len(self.crawled_urls_history)
            }
            with open(self.crawled_urls_file, 'w', encoding='utf-8') as f:
                json.dump(url_data, f, ensure_ascii=False, indent=2)
            
            # 保存批次统计
            with open(self.batch_stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.batch_stats, f, ensure_ascii=False, indent=2)
                
            print(f"💾 历史记录已保存")
                
        except Exception as e:
            print(f"❌ 保存历史记录失败: {e}")
    
    def get_available_urls(self, start_url: str, max_search: int = 100) -> List[str]:
        """获取可用的文章URL列表"""
        print(f"🔍 搜索相关文章URL...")
        
        # 这里使用原有的搜索逻辑
        # 您可以根据需要调整搜索策略
        all_urls = []
        
        try:
            # 使用原有的搜索方法获取URL
            # 这里需要根据您的 WeChatCrawlerWithImages 类的实际搜索方法来调整
            
            # 示例：如果有搜索方法
            if hasattr(self, 'search_related_articles'):
                all_urls = self.search_related_articles(start_url, max_search)
            else:
                # 如果没有搜索方法，至少包含起始URL
                all_urls = [start_url]
                print(f"⚠️  未找到搜索方法，仅使用起始URL")
            
        except Exception as e:
            print(f"❌ 搜索URL失败: {e}")
            all_urls = [start_url]
        
        print(f"🎯 找到 {len(all_urls)} 个潜在URL")
        return all_urls
    
    def crawl_articles_batch(self, start_url: str, max_articles: int = 10, skip_count: int = 0) -> List[str]:
        """
        分批爬取文章
        
        Args:
            start_url: 起始文章URL
            max_articles: 本批次要爬取的文章数量
            skip_count: 要跳过的文章数量
            
        Returns:
            保存的文件路径列表
        """
        print(f"🚀 开始分批爬取")
        print(f"📄 起始URL: {start_url}")
        print(f"🎯 本批目标: {max_articles} 篇文章")
        print(f"⏭️  跳过数量: {skip_count} 篇")
        print(f"📚 历史已爬取: {len(self.crawled_urls_history)} 个URL")
        print("-" * 80)
        
        # 获取所有可用URL
        all_urls = self.get_available_urls(start_url, max_articles * 3)
        
        # 过滤已爬取的URL
        new_urls = [url for url in all_urls if url not in self.crawled_urls_history]
        print(f"🆕 发现新URL: {len(new_urls)} 个")
        
        # 应用跳过逻辑
        if skip_count > 0:
            if len(new_urls) > skip_count:
                urls_to_crawl = new_urls[skip_count:skip_count + max_articles]
                print(f"⏭️  跳过前 {skip_count} 个URL")
                print(f"📋 准备爬取第 {skip_count + 1} 到第 {skip_count + len(urls_to_crawl)} 个URL")
            else:
                print(f"⚠️  跳过数量({skip_count})超过可用URL数量({len(new_urls)})")
                return []
        else:
            urls_to_crawl = new_urls[:max_articles]
            print(f"📋 准备爬取前 {len(urls_to_crawl)} 个URL")
        
        if not urls_to_crawl:
            print(f"❌ 没有可爬取的URL")
            return []
        
        # 开始爬取
        saved_files = []
        successful_count = 0
        
        for i, url in enumerate(urls_to_crawl, 1):
            print(f"\n📊 进度: {i}/{len(urls_to_crawl)} | 已成功: {successful_count}/{max_articles}")
            print(f"🔗 爬取URL: {url}")
            
            try:
                # 爬取单篇文章
                article_data = self.crawl_single_article(url)
                
                if article_data:
                    # 保存文章
                    saved_file = self.save_article(article_data)
                    if saved_file:
                        saved_files.append(saved_file)
                        successful_count += 1
                        
                        # 添加到已爬取历史
                        self.crawled_urls_history.add(url)
                        
                        print(f"✅ 成功爬取: {article_data['title']}")
                        if article_data.get('image_count', 0) > 0:
                            print(f"🖼️  下载图片: {article_data['image_count']} 张")
                    
                    # 达到目标数量就停止
                    if successful_count >= max_articles:
                        print(f"🎯 已达到本批目标数量: {max_articles}")
                        break
                else:
                    print(f"❌ 爬取失败，跳过此URL")
                    # 即使失败也标记为已处理，避免重复尝试
                    self.crawled_urls_history.add(url)
                
                # 添加延迟避免被封
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ 爬取URL失败: {e}")
                self.crawled_urls_history.add(url)
                continue
        
        # 更新批次统计
        self.batch_stats['total_batches'] += 1
        self.batch_stats['total_articles'] += successful_count
        self.batch_stats['total_images'] += sum(article.get('image_count', 0) for article in self.articles[-successful_count:])
        self.batch_stats['last_batch_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
        
        # 保存历史记录
        self.save_history()
        
        # 生成批次总结
        self.generate_batch_summary(saved_files, skip_count, max_articles)
        
        print(f"\n🎉 本批次爬取完成！")
        print(f"📊 成功爬取: {successful_count} 篇文章")
        print(f"📁 保存目录: {self.output_dir}")
        
        return saved_files
    
    def generate_batch_summary(self, saved_files: List[str], skip_count: int, target_count: int):
        """生成批次总结"""
        try:
            batch_num = self.batch_stats['total_batches']
            
            summary_content = f"""# 分批爬取总结 - 第 {batch_num} 批次

**爬取时间:** {time.strftime('%Y-%m-%d %H:%M:%S')}
**批次编号:** {batch_num}
**跳过文章:** {skip_count} 篇
**目标数量:** {target_count} 篇
**实际爬取:** {len(saved_files)} 篇
**保存目录:** {self.output_dir}

## 批次统计
- **总批次数:** {self.batch_stats['total_batches']}
- **累计文章:** {self.batch_stats['total_articles']} 篇
- **累计图片:** {self.batch_stats['total_images']} 张
- **已处理URL:** {len(self.crawled_urls_history)} 个

## 本批次文章列表

"""
            
            # 获取本批次的文章（最后几篇）
            batch_articles = self.articles[-len(saved_files):] if saved_files else []
            
            for i, article in enumerate(batch_articles, 1):
                image_info = ""
                if article.get('image_count', 0) > 0:
                    image_info = f" (含 {article['image_count']} 张图片)"
                
                summary_content += f"{i}. **{article['title']}**{image_info}\n"
                summary_content += f"   - 作者: {article.get('author', '未知')}\n"
                summary_content += f"   - 发布时间: {article.get('publish_time', '未知')}\n"
                summary_content += f"   - 链接: {article['url']}\n\n"
            
            summary_content += f"""
## 保存的文件

"""
            
            for i, filepath in enumerate(saved_files, 1):
                filename = Path(filepath).name
                summary_content += f"{i}. {filename}\n"
            
            # 计算下一批次的跳过数量
            next_skip = skip_count + len(saved_files)
            
            summary_content += f"""

## 继续爬取下一批次

```bash
# 下一批次命令（跳过前 {next_skip} 篇）
python wechat_crawler_cli.py -u "起始URL" -n {target_count} --skip {next_skip}
```

---
*由分批微信爬虫生成*
"""
            
            # 保存总结文件
            summary_filename = f"批次总结_{batch_num}_{time.strftime('%Y%m%d_%H%M%S')}.md"
            summary_path = self.output_dir / summary_filename
            
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary_content)
            
            print(f"📋 批次总结已保存: {summary_filename}")
            
        except Exception as e:
            print(f"❌ 生成批次总结失败: {e}")
    
    def show_batch_history(self):
        """显示批次历史"""
        print("📊 分批爬取历史统计:")
        print(f"   📚 总批次数: {self.batch_stats['total_batches']}")
        print(f"   📄 累计文章: {self.batch_stats['total_articles']} 篇")
        print(f"   🖼️  累计图片: {self.batch_stats['total_images']} 张")
        print(f"   🔗 已处理URL: {len(self.crawled_urls_history)} 个")
        print(f"   🕒 最后批次: {self.batch_stats.get('last_batch_time', '未知')}")
        print(f"   📁 历史文件: {self.crawled_urls_file}")
    
    def clear_history(self):
        """清除历史记录"""
        try:
            files_to_remove = [self.crawled_urls_file, self.batch_stats_file]
            removed_count = 0
            
            for file_path in files_to_remove:
                if file_path.exists():
                    file_path.unlink()
                    removed_count += 1
            
            # 重置内存中的数据
            self.crawled_urls_history.clear()
            self.batch_stats = {
                'total_batches': 0,
                'total_articles': 0,
                'total_images': 0,
                'last_batch_time': None
            }
            
            if removed_count > 0:
                print(f"✅ 历史记录已清除 ({removed_count} 个文件)")
            else:
                print("📝 暂无历史记录需要清除")
                
        except Exception as e:
            print(f"❌ 清除历史记录失败: {e}")
