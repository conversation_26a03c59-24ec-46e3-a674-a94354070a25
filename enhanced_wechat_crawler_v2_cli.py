#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版微信公众号文章爬虫V2命令行工具
专门解决微信反爬机制和URL获取限制问题
"""

import argparse
import sys
import os
from enhanced_wechat_crawler_v2 import EnhancedWeChatCrawlerV2

def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(
        description='增强版微信公众号文章爬虫V2 - 专门解决反爬机制和URL获取限制',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本用法
  python enhanced_wechat_crawler_v2_cli.py -u "https://mp.weixin.qq.com/s/H8QXo_rVSX4HCve1rxESNw"
  
  # 指定爬取数量和输出目录
  python enhanced_wechat_crawler_v2_cli.py -u "https://mp.weixin.qq.com/s/example" -n 50 -o "D:/articles"
  
  # 详细输出模式
  python enhanced_wechat_crawler_v2_cli.py -u "https://mp.weixin.qq.com/s/example" -v

核心优势:
  🚀 多搜索引擎发现文章URL (百度、搜狗、必应、360)
  🛡️ 智能反爬机制 (随机User-Agent、延迟、请求头)
  🔍 内容URL提取 (从文章内容中发现更多链接)
  📊 智能去重过滤 (避免重复爬取)
  🎯 目标账号验证 (确保爬取正确的公众号文章)
  
解决的问题:
  ❌ 微信不显示公众号主页URL
  ❌ 搜索引擎结果有限
  ❌ 手动复制URL效率低
  ❌ 反爬机制导致失败
        """
    )

    parser.add_argument(
        '-u', '--url',
        required=True,
        help='微信公众号文章URL（起始文章）'
    )

    parser.add_argument(
        '-n', '--num-articles',
        type=int,
        default=100,
        help='要爬取的文章数量 (默认: 100)'
    )

    parser.add_argument(
        '-o', '--output-dir',
        default="C:/Users/<USER>/Documents/Obsidian Vault/微信好文",
        help='输出目录 (默认: C:/Users/<USER>/Documents/Obsidian Vault/微信好文)'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细输出'
    )

    parser.add_argument(
        '--min-delay',
        type=float,
        default=3.0,
        help='最小延迟时间（秒）(默认: 3.0)'
    )

    parser.add_argument(
        '--max-delay',
        type=float,
        default=8.0,
        help='最大延迟时间（秒）(默认: 8.0)'
    )

    args = parser.parse_args()

    # 验证URL
    if not args.url.startswith('http'):
        print("❌ 错误: URL必须以http或https开头")
        sys.exit(1)

    if 'mp.weixin.qq.com' not in args.url:
        print("❌ 错误: 请提供有效的微信公众号文章URL")
        sys.exit(1)

    # 验证延迟参数
    if args.min_delay >= args.max_delay:
        print("❌ 错误: 最小延迟时间必须小于最大延迟时间")
        sys.exit(1)

    # 显示配置信息
    print("🚀 增强版微信公众号文章爬虫V2启动...")
    print(f"📄 起始文章URL: {args.url}")
    print(f"🎯 目标文章数: {args.num_articles}")
    print(f"📁 输出目录: {args.output_dir}")
    print(f"⏱️  延迟范围: {args.min_delay}-{args.max_delay}秒")
    print("-" * 80)
    print("🔧 核心特性:")
    print("   ✅ 多搜索引擎发现 (百度、搜狗、必应、360)")
    print("   ✅ 智能反爬机制 (随机UA、延迟、请求头)")
    print("   ✅ 内容URL提取 (从文章中发现更多链接)")
    print("   ✅ 智能去重过滤 (避免重复爬取)")
    print("   ✅ 目标账号验证 (确保爬取正确文章)")
    print("-" * 80)

    try:
        # 创建增强版爬虫实例
        crawler = EnhancedWeChatCrawlerV2(output_dir=args.output_dir)
        
        # 设置延迟参数
        crawler.min_delay = args.min_delay
        crawler.max_delay = args.max_delay

        # 开始增强版爬取
        print("🚀 正在启动多引擎搜索和智能爬取...")
        saved_files = crawler.crawl_articles_enhanced(args.url, args.num_articles)

        # 显示结果
        print("\n" + "="*80)
        if saved_files:
            print("🎉 增强版爬取任务完成！")
            print(f"📊 成功爬取文章: {len(saved_files)} 篇")
            print(f"📁 保存目录: {args.output_dir}")
            
            # 显示技术统计
            print(f"\n📈 技术统计:")
            print(f"   🔍 URL池大小: {len(crawler.url_pool)}")
            print(f"   📋 已爬取URL: {len(crawler.crawled_urls)}")
            if crawler.crawled_urls:
                success_rate = len(crawler.articles) / len(crawler.crawled_urls) * 100
                print(f"   ✅ 成功率: {success_rate:.1f}%")
            
            # 显示账号信息
            if crawler.account_info:
                print(f"\n📱 公众号信息:")
                print(f"   📝 名称: {crawler.account_info.get('name', '未知')}")
                print(f"   🔑 BIZ: {crawler.account_info.get('biz', '未获取')}")
            
            if args.verbose and saved_files:
                print("\n📚 爬取的文章列表:")
                for i, filepath in enumerate(saved_files, 1):
                    filename = os.path.basename(filepath)
                    # 从文件名中提取标题（移除时间戳）
                    title = filename.replace('.md', '').rsplit('_', 2)[0]
                    print(f"  {i:2d}. {title}")
            
            print(f"\n💡 使用提示:")
            print(f"   - 文章已保存为Markdown格式，可在Obsidian等工具中查看")
            print(f"   - 总结文件包含详细的爬取统计和技术信息")
            print(f"   - 如需爬取更多文章，可以调整-n参数")
            print(f"   - 如遇到反爬限制，可以增加延迟时间")
        else:
            print("❌ 爬取失败或未找到任何文章")
            print("\n💡 可能的原因:")
            print("   - 网络连接问题")
            print("   - 微信反爬机制拦截")
            print("   - URL无效或文章已删除")
            print("   - 搜索引擎暂时不可用")
            print("\n🔧 建议解决方案:")
            print("   1. 检查网络连接")
            print("   2. 增加延迟时间: --min-delay 5 --max-delay 10")
            print("   3. 尝试不同的起始文章URL")
            print("   4. 稍后重试")
        
        print("="*80)

    except KeyboardInterrupt:
        print("\n⚠️  用户中断了爬取过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 爬取过程中出现错误: {str(e)}")
        print("\n💡 故障排除:")
        print("   1. 检查网络连接")
        print("   2. 确认URL格式正确")
        print("   3. 尝试增加延迟时间")
        print("   4. 检查输出目录权限")
        
        if args.verbose:
            import traceback
            print(f"\n🔍 详细错误信息:")
            traceback.print_exc()
        
        sys.exit(1)


if __name__ == "__main__":
    main()
