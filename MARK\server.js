const express = require('express');
const multer = require('multer');
const mammoth = require('mammoth');
const xlsx = require('xlsx');
const fs = require('fs-extra');
const cors = require('cors');
const path = require('path');
const app = express();

app.use(cors());
const upload = multer({ dest: 'uploads/' });

// 提供静态文件服务（用于访问index.html等前端文件）
app.use(express.static('.'));
const outputDir = 'E:\\mcp-test\\MARK';

// 转换历史记录
let conversionHistory = [];

// Word转Markdown
async function wordToMd(filePath, outputPath) {
  try {
    const result = await mammoth.convertToMarkdown({ path: filePath });
    await fs.writeFile(outputPath, result.value);
    return { success: true, message: '转换成功' };
  } catch (error) {
    return { success: false, message: `转换失败: ${error.message}` };
  }
}

// Excel转Markdown
async function excelToMd(filePath, outputPath) {
  try {
    const workbook = xlsx.readFile(filePath);
    let markdown = '';
    workbook.SheetNames.forEach(sheetName => {
      const sheet = workbook.Sheets[sheetName];
      const data = xlsx.utils.sheet_to_json(sheet, { header: 1 });
      markdown += `## ${sheetName}\n\n`;
      markdown += '| ' + data[0].join(' | ') + ' |\n';
      markdown += '|' + data[0].map(() => '---').join('|') + '|\n';
      data.slice(1).forEach(row => {
        markdown += '| ' + row.join(' | ') + ' |\n';
      });
      markdown += '\n\n';
    });
    await fs.writeFile(outputPath, markdown);
    return { success: true, message: '转换成功' };
  } catch (error) {
    return { success: false, message: `转换失败: ${error.message}` };
  }
}

// 文件上传接口
app.post('/upload', upload.array('files'), async (req, res) => {
  const results = [];
  for (const file of req.files) {
    const inputPath = file.path;
    const ext = path.extname(file.originalname).toLowerCase();
    const outputName = path.basename(file.originalname, ext) + '.md';
    const outputPath = path.join(outputDir, outputName);

    let result;
    if (ext === '.docx') {
      result = await wordToMd(inputPath, outputPath);
    } else if (ext === '.xlsx') {
      result = await excelToMd(inputPath, outputPath);
    } else {
      result = { success: false, message: '不支持的文件类型' };
    }

    results.push({ filename: file.originalname, ...result });
    if (result.success) {
      conversionHistory.push({ time: new Date().toISOString(), file: file.originalname, output: outputName });
    }
    await fs.remove(inputPath);
  }
  res.json({ results, history: conversionHistory });
});

const server = app.listen(3004, () => {
  console.log('服务器运行在端口3004');
});