#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超安全微信公众号文章爬虫命令行工具
专门应对微信名誉保护和强力反爬机制
"""

import argparse
import sys
import os
from ultra_safe_wechat_crawler import UltraSafeWeChatCrawler

def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(
        description='超安全微信公众号文章爬虫 - 专门应对名誉保护和强力反爬机制',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本用法（超安全模式）
  python ultra_safe_wechat_crawler_cli.py -u "https://mp.weixin.qq.com/s/H8QXo_rVSX4HCve1rxESNw"
  
  # 指定爬取数量（建议不超过20）
  python ultra_safe_wechat_crawler_cli.py -u "https://mp.weixin.qq.com/s/example" -n 15
  
  # 自定义延迟时间（应对强力反爬）
  python ultra_safe_wechat_crawler_cli.py -u "https://mp.weixin.qq.com/s/example" --min-delay 15 --max-delay 30
  
  # 详细输出模式
  python ultra_safe_wechat_crawler_cli.py -u "https://mp.weixin.qq.com/s/example" -v

超安全特性:
  🛡️ 超保守延迟策略 (10-20秒基础延迟)
  🔍 智能保护页面检测 (自动识别名誉保护页面)
  ⏱️ 防护性休息机制 (每10个请求休息1-2分钟)
  📊 有效文章验证 (过滤保护页面和无效内容)
  🎯 保守搜索策略 (限制搜索数量避免触发保护)
  
专门解决:
  ❌ 名誉保护投诉指引页面
  ❌ 访问过于频繁限制
  ❌ 系统检测异常提示
  ❌ 人机验证拦截
        """
    )

    parser.add_argument(
        '-u', '--url',
        required=True,
        help='微信公众号文章URL（起始文章）'
    )

    parser.add_argument(
        '-n', '--num-articles',
        type=int,
        default=20,
        help='要爬取的文章数量 (默认: 20, 建议不超过50)'
    )

    parser.add_argument(
        '-o', '--output-dir',
        default="C:/Users/<USER>/Documents/Obsidian Vault/微信好文",
        help='输出目录 (默认: C:/Users/<USER>/Documents/Obsidian Vault/微信好文)'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细输出'
    )

    parser.add_argument(
        '--min-delay',
        type=float,
        default=10.0,
        help='最小延迟时间（秒）(默认: 10.0)'
    )

    parser.add_argument(
        '--max-delay',
        type=float,
        default=20.0,
        help='最大延迟时间（秒）(默认: 20.0)'
    )

    parser.add_argument(
        '--search-delay',
        type=float,
        default=30.0,
        help='搜索间隔时间（秒）(默认: 30.0)'
    )

    args = parser.parse_args()

    # 验证URL
    if not args.url.startswith('http'):
        print("❌ 错误: URL必须以http或https开头")
        sys.exit(1)

    if 'mp.weixin.qq.com' not in args.url:
        print("❌ 错误: 请提供有效的微信公众号文章URL")
        sys.exit(1)

    # 验证延迟参数
    if args.min_delay >= args.max_delay:
        print("❌ 错误: 最小延迟时间必须小于最大延迟时间")
        sys.exit(1)

    # 验证文章数量
    if args.num_articles > 100:
        print("⚠️  警告: 文章数量过多可能触发强力保护机制")
        print("💡 建议: 将数量控制在50篇以内")
        
        confirm = input("是否继续? (y/N): ").strip().lower()
        if confirm != 'y':
            sys.exit(0)

    # 显示配置信息
    print("🛡️  超安全微信公众号文章爬虫启动...")
    print(f"📄 起始文章URL: {args.url}")
    print(f"🎯 目标文章数: {args.num_articles}")
    print(f"📁 输出目录: {args.output_dir}")
    print(f"⏱️  基础延迟: {args.min_delay}-{args.max_delay}秒")
    print(f"🔍 搜索间隔: {args.search_delay}秒")
    print("-" * 80)
    print("🛡️  超安全特性:")
    print("   ✅ 智能保护页面检测 (自动识别名誉保护页面)")
    print("   ✅ 防护性休息机制 (每10个请求休息1-2分钟)")
    print("   ✅ 有效文章验证 (过滤保护页面和无效内容)")
    print("   ✅ 保守搜索策略 (限制搜索数量避免触发保护)")
    print("   ✅ 超保守延迟策略 (大幅延长请求间隔)")
    print("-" * 80)

    try:
        # 创建超安全爬虫实例
        crawler = UltraSafeWeChatCrawler(output_dir=args.output_dir)
        
        # 设置延迟参数
        crawler.min_delay = args.min_delay
        crawler.max_delay = args.max_delay
        crawler.search_delay = args.search_delay

        # 开始超安全爬取
        print("🛡️  正在启动超安全爬取模式...")
        print("⚠️  注意: 此模式延迟较长，请耐心等待...")
        
        saved_files = crawler.crawl_articles_ultra_safe(args.url, args.num_articles)

        # 显示结果
        print("\n" + "="*80)
        if saved_files:
            print("🎉 超安全爬取任务完成！")
            print(f"📊 成功爬取文章: {len(saved_files)} 篇")
            print(f"📁 保存目录: {args.output_dir}")
            
            # 显示安全统计
            print(f"\n🛡️  安全统计:")
            print(f"   📊 总请求数: {crawler.request_count}")
            print(f"   ⏱️ 平均延迟: {(args.min_delay + args.max_delay) / 2:.1f}秒")
            print(f"   🔍 已爬取URL: {len(crawler.crawled_urls)}")
            if crawler.crawled_urls:
                success_rate = len(crawler.articles) / len(crawler.crawled_urls) * 100
                print(f"   ✅ 成功率: {success_rate:.1f}%")
            
            # 显示账号信息
            if crawler.account_info:
                print(f"\n📱 公众号信息:")
                print(f"   📝 名称: {crawler.account_info.get('name', '未知')}")
                biz_status = "已获取" if crawler.account_info.get('biz') else "未获取"
                print(f"   🔑 BIZ: {biz_status}")
            
            if args.verbose and saved_files:
                print("\n📚 爬取的文章列表:")
                for i, filepath in enumerate(saved_files, 1):
                    filename = os.path.basename(filepath)
                    # 从文件名中提取标题（移除时间戳）
                    title = filename.replace('.md', '').rsplit('_', 2)[0]
                    print(f"  {i:2d}. {title}")
            
            print(f"\n💡 使用提示:")
            print(f"   - 文章已保存为Markdown格式，可在Obsidian等工具中查看")
            print(f"   - 超安全模式有效避免了名誉保护页面")
            print(f"   - 如需爬取更多文章，建议等待一段时间后重新运行")
            print(f"   - 总结文件包含详细的安全统计信息")
        else:
            print("❌ 超安全爬取失败")
            print("\n💡 可能的原因:")
            print("   - 起始文章本身就是保护页面")
            print("   - 网络连接问题")
            print("   - 微信实施了更强的保护机制")
            print("   - 搜索引擎暂时不可用")
            print("\n🔧 建议解决方案:")
            print("   1. 尝试不同的起始文章URL")
            print("   2. 进一步增加延迟时间:")
            print("      --min-delay 20 --max-delay 40 --search-delay 60")
            print("   3. 减少目标文章数量: -n 10")
            print("   4. 等待更长时间后重试")
            print("   5. 检查网络连接和代理设置")
        
        print("="*80)

    except KeyboardInterrupt:
        print("\n⚠️  用户中断了爬取过程")
        print("💡 提示: 超安全模式需要较长时间，建议耐心等待")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 爬取过程中出现错误: {str(e)}")
        print("\n💡 故障排除:")
        print("   1. 检查网络连接")
        print("   2. 确认URL格式正确")
        print("   3. 尝试进一步增加延迟时间")
        print("   4. 检查输出目录权限")
        print("   5. 尝试使用不同的起始文章")
        
        if args.verbose:
            import traceback
            print(f"\n🔍 详细错误信息:")
            traceback.print_exc()
        
        sys.exit(1)


if __name__ == "__main__":
    main()
