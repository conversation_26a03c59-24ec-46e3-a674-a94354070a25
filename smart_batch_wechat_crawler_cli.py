#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能分批微信公众号文章爬虫命令行工具
优化速度控制，提高下载成功率，减少触发微信保护机制
"""

import argparse
import sys
import os
import json
from pathlib import Path
from smart_batch_wechat_crawler import SmartBatchWeChatCrawler

def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(
        description='智能分批微信公众号文章爬虫 - 优化速度控制，提高成功率',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 第一批：爬取前15篇（智能模式）
  python smart_batch_wechat_crawler_cli.py -u "URL" -n 15
  
  # 第二批：跳过前15篇，爬取第16-30篇
  python smart_batch_wechat_crawler_cli.py -u "URL" -n 15 --skip 15
  
  # 查看智能统计
  python smart_batch_wechat_crawler_cli.py --show-stats
  
  # 重置智能学习数据
  python smart_batch_wechat_crawler_cli.py --reset-stats

智能特性:
  🧠 动态延迟调整 (根据成功率自动调整延迟时间)
  📊 成功率监控 (实时监控并优化爬取策略)
  🛡️ 智能保护检测 (自动识别并应对各种保护机制)
  🔄 自适应策略 (根据历史表现调整搜索和爬取策略)
  📈 学习优化 (从每次爬取中学习，持续改进)
  
速度控制优化:
  ⏱️ 基础延迟15秒，最大30秒
  🔄 根据成功率动态调整延迟倍数(1.0-3.0)
  🛡️ 连续失败5次后进入保护模式
  📊 每5篇文章后批次休息
  ⚠️ 保护机制触发时大幅增加延迟
        """
    )

    parser.add_argument(
        '-u', '--url',
        help='微信公众号文章URL（起始文章）'
    )

    parser.add_argument(
        '-n', '--num-articles',
        type=int,
        default=15,
        help='本批要爬取的文章数量 (默认: 15, 建议不超过30)'
    )

    parser.add_argument(
        '--skip',
        type=int,
        default=0,
        help='跳过的文章数量 (默认: 0)'
    )

    parser.add_argument(
        '-o', '--output-dir',
        default="C:/Users/<USER>/Documents/Obsidian Vault/微信好文",
        help='输出目录 (默认: C:/Users/<USER>/Documents/Obsidian Vault/微信好文)'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细输出'
    )

    parser.add_argument(
        '--base-delay',
        type=float,
        default=15.0,
        help='基础延迟时间（秒）(默认: 15.0)'
    )

    parser.add_argument(
        '--max-delay',
        type=float,
        default=30.0,
        help='最大延迟时间（秒）(默认: 30.0)'
    )

    parser.add_argument(
        '--show-stats',
        action='store_true',
        help='显示智能统计信息'
    )

    parser.add_argument(
        '--reset-stats',
        action='store_true',
        help='重置智能学习数据'
    )

    parser.add_argument(
        '--conservative',
        action='store_true',
        help='使用保守模式（更长延迟，更高成功率）'
    )

    args = parser.parse_args()

    # 创建爬虫实例
    crawler = SmartBatchWeChatCrawler(output_dir=args.output_dir)

    # 显示智能统计
    if args.show_stats:
        print("🧠 智能爬虫统计信息:")
        if crawler.stats_file.exists():
            try:
                with open(crawler.stats_file, 'r', encoding='utf-8') as f:
                    stats = json.load(f)
                    print(f"   📊 成功率: {stats.get('success_rate', 0):.1f}%")
                    print(f"   ✅ 成功次数: {stats.get('success_count', 0)}")
                    print(f"   ❌ 失败次数: {stats.get('failure_count', 0)}")
                    print(f"   🛡️ 保护触发: {stats.get('protection_count', 0)}")
                    print(f"   🔄 延迟倍数: {stats.get('delay_multiplier', 1.0):.1f}")
                    print(f"   🕒 最后更新: {stats.get('last_update', '未知')}")
                    print(f"   📁 统计文件: {crawler.stats_file}")
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        else:
            print("   📝 暂无统计数据")
        
        if crawler.crawled_urls_file.exists():
            try:
                with open(crawler.crawled_urls_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(f"   📚 已爬取URL: {len(data.get('crawled_urls', []))}")
                    print(f"   📄 总文章数: {data.get('total_articles', 0)}")
                    account_info = data.get('account_info', {})
                    if account_info.get('name'):
                        print(f"   📱 公众号: {account_info['name']}")
            except Exception as e:
                print(f"   ❌ 读取URL历史失败: {e}")
        return

    # 重置统计数据
    if args.reset_stats:
        try:
            files_to_remove = [crawler.stats_file, crawler.crawled_urls_file]
            removed_count = 0
            for file_path in files_to_remove:
                if file_path.exists():
                    file_path.unlink()
                    removed_count += 1
            
            if removed_count > 0:
                print(f"✅ 已重置智能学习数据 ({removed_count} 个文件)")
            else:
                print("📝 暂无数据需要重置")
        except Exception as e:
            print(f"❌ 重置数据失败: {e}")
        return

    # 验证必需参数
    if not args.url:
        print("❌ 错误: 必须提供起始文章URL")
        print("💡 使用 --show-stats 查看智能统计")
        print("💡 使用 --reset-stats 重置学习数据")
        sys.exit(1)

    # 验证URL
    if not args.url.startswith('http'):
        print("❌ 错误: URL必须以http或https开头")
        sys.exit(1)

    if 'mp.weixin.qq.com' not in args.url:
        print("❌ 错误: 请提供有效的微信公众号文章URL")
        sys.exit(1)

    # 验证文章数量
    if args.num_articles > 50:
        print("⚠️  警告: 单批文章数量过多可能触发强力保护机制")
        print("💡 建议: 将数量控制在30篇以内")
        
        confirm = input("是否继续? (y/N): ").strip().lower()
        if confirm != 'y':
            sys.exit(0)

    # 应用保守模式
    if args.conservative:
        crawler.base_delay = max(args.base_delay, 25)
        crawler.max_delay = max(args.max_delay, 50)
        crawler.search_delay = max(crawler.search_delay, 90)
        crawler.current_delay_multiplier = max(crawler.current_delay_multiplier, 1.5)
        print("🛡️  保守模式已启用")

    # 设置延迟参数
    crawler.base_delay = args.base_delay
    crawler.max_delay = args.max_delay

    # 显示配置信息
    print("🧠 智能分批微信公众号文章爬虫启动...")
    print(f"📄 起始文章URL: {args.url}")
    print(f"🎯 本批目标数量: {args.num_articles}")
    print(f"⏭️  跳过文章数: {args.skip}")
    print(f"📁 输出目录: {args.output_dir}")
    print(f"⏱️  基础延迟: {crawler.base_delay}秒")
    print(f"📊 历史成功率: {crawler.get_success_rate():.1f}%")
    print(f"🔄 当前延迟倍数: {crawler.current_delay_multiplier:.1f}")
    print("-" * 80)
    print("🧠 智能特性:")
    print("   ✅ 动态延迟调整 (根据成功率自动调整)")
    print("   ✅ 成功率监控 (实时优化爬取策略)")
    print("   ✅ 智能保护检测 (自动应对保护机制)")
    print("   ✅ 自适应策略 (根据历史表现调整)")
    print("   ✅ 学习优化 (持续改进爬取效果)")
    print("-" * 80)

    try:
        # 开始智能分批爬取
        print("🧠 正在启动智能爬取模式...")
        print("⚠️  注意: 智能模式会根据成功率自动调整延迟...")
        
        saved_files = crawler.crawl_articles_smart_batch(args.url, args.num_articles, args.skip)

        # 显示结果
        print("\n" + "="*80)
        if saved_files:
            print("🎉 智能分批爬取任务完成！")
            print(f"📊 本批成功爬取: {len(saved_files)} 篇文章")
            print(f"📁 保存目录: {args.output_dir}")
            
            # 显示智能统计
            print(f"\n🧠 智能统计:")
            print(f"   📈 当前成功率: {crawler.get_success_rate():.1f}%")
            print(f"   ✅ 成功爬取: {crawler.success_count} 篇")
            print(f"   ❌ 失败次数: {crawler.failure_count} 次")
            print(f"   🛡️ 保护触发: {crawler.protection_count} 次")
            print(f"   🔄 延迟倍数: {crawler.current_delay_multiplier:.1f}")
            print(f"   📊 总请求数: {crawler.request_count}")
            
            # 显示账号信息
            if crawler.account_info:
                print(f"\n📱 公众号信息:")
                print(f"   📝 名称: {crawler.account_info.get('name', '未知')}")
                biz_status = "已获取" if crawler.account_info.get('biz') else "未获取"
                print(f"   🔑 BIZ: {biz_status}")
            
            if args.verbose and saved_files:
                print("\n📚 本批爬取的文章:")
                for i, filepath in enumerate(saved_files, 1):
                    filename = os.path.basename(filepath)
                    title = filename.replace('.md', '').rsplit('_', 2)[0]
                    print(f"  {i:2d}. {title}")
            
            # 智能建议
            print(f"\n🧠 智能建议:")
            success_rate = crawler.get_success_rate()
            if success_rate < 50:
                print(f"   ⚠️ 成功率较低({success_rate:.1f}%)，建议:")
                print(f"      - 增加延迟时间: --base-delay 25 --max-delay 50")
                print(f"      - 减少单批数量: -n 10")
                print(f"      - 使用保守模式: --conservative")
            elif success_rate > 80:
                print(f"   ✅ 成功率良好({success_rate:.1f}%)，可以:")
                print(f"      - 适当增加爬取数量: -n 20")
                print(f"      - 保持当前策略")
            
            # 下一批建议
            if len(saved_files) > 0:
                next_skip = args.skip + len(saved_files)
                print(f"\n💡 继续爬取下一批:")
                print(f"   python smart_batch_wechat_crawler_cli.py -u \"{args.url}\" -n {args.num_articles} --skip {next_skip}")
                if crawler.current_delay_multiplier > 1.5:
                    print(f"   建议使用保守模式: --conservative")
            
        else:
            print("❌ 智能分批爬取失败")
            print("\n💡 可能的原因:")
            print("   - 所有文章都已爬取过")
            print("   - 跳过数量超过了可用文章数")
            print("   - 遇到强力反爬机制")
            print("   - 网络连接问题")
            
            print("\n🧠 智能建议:")
            print("   1. 查看统计信息: --show-stats")
            print("   2. 尝试保守模式: --conservative")
            print("   3. 增加延迟时间: --base-delay 30")
            print("   4. 等待更长时间后重试")
            print("   5. 尝试不同的起始文章")
        
        print("="*80)

    except KeyboardInterrupt:
        print("\n⚠️  用户中断了爬取过程")
        print("💡 提示: 智能模式的学习数据已保存，下次运行会继续优化")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 爬取过程中出现错误: {str(e)}")
        print("\n💡 故障排除:")
        print("   1. 检查网络连接")
        print("   2. 查看智能统计: --show-stats")
        print("   3. 尝试保守模式: --conservative")
        print("   4. 重置学习数据: --reset-stats")
        
        if args.verbose:
            import traceback
            print(f"\n🔍 详细错误信息:")
            traceback.print_exc()
        
        sys.exit(1)


if __name__ == "__main__":
    main()
