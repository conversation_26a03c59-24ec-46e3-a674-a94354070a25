#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频字幕提取器
批量提取视频文件中的字幕并转换为.dm格式
支持GLM-4-Flash API翻译和处理
"""

import os
import sys
import subprocess
import json
import re
import requests
import time
from pathlib import Path
from typing import List, Dict, Optional
import argparse
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('subtitle_extractor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GLMTranslator:
    """GLM-4-Flash API翻译器"""

    def __init__(self, api_key: str, base_url: str = "https://open.bigmodel.cn/api/paas/v4"):
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def translate_text(self, text: str, target_lang: str = "中文") -> str:
        """翻译文本"""
        try:
            url = f"{self.base_url}/chat/completions"

            prompt = f"请将以下字幕文本翻译成{target_lang}，保持原有的时间轴格式，只翻译文本内容：\n\n{text}"

            data = {
                "model": "GLM-4-Flash",
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 2000
            }

            response = requests.post(url, headers=self.headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result['choices'][0]['message']['content'].strip()

        except Exception as e:
            logger.error(f"翻译失败: {e}")
            return text  # 返回原文本

    def process_subtitle_content(self, content: str, operation: str = "translate") -> str:
        """处理字幕内容"""
        try:
            url = f"{self.base_url}/chat/completions"

            if operation == "translate":
                prompt = f"请将以下字幕翻译成中文，保持SRT格式不变：\n\n{content}"
            elif operation == "clean":
                prompt = f"请清理以下字幕内容，去除无关符号和噪音，保持SRT格式：\n\n{content}"
            elif operation == "enhance":
                prompt = f"请优化以下字幕的可读性，修正错误，保持SRT格式：\n\n{content}"
            else:
                return content

            data = {
                "model": "GLM-4-Flash",
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 4000
            }

            response = requests.post(url, headers=self.headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            return result['choices'][0]['message']['content'].strip()

        except Exception as e:
            logger.error(f"字幕处理失败: {e}")
            return content

class VideoSubtitleExtractor:
    """视频字幕提取器类"""

    def __init__(self, video_dir: str = "vidoin", glm_api_key: str = None, glm_base_url: str = None):
        self.video_dir = Path(video_dir)
        self.supported_formats = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.m4v', '.webm', '.ts', '.mts'}
        self.subtitle_formats = {'.srt', '.ass', '.ssa', '.vtt', '.sub'}

        # GLM翻译器
        self.translator = None
        if glm_api_key:
            base_url = glm_base_url or "https://open.bigmodel.cn/api/paas/v4"
            self.translator = GLMTranslator(glm_api_key, base_url)
            logger.info("GLM-4-Flash翻译器已启用")

        # 确保目录存在
        self.video_dir.mkdir(exist_ok=True)

    def check_ffmpeg(self) -> bool:
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(['ffmpeg', '-version'],
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def get_video_files(self) -> List[Path]:
        """获取目录中的所有视频文件"""
        video_files = []
        for file_path in self.video_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                video_files.append(file_path)
        return sorted(video_files)

    def get_subtitle_info(self, video_path: Path) -> List[Dict]:
        """获取视频文件的字幕信息"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_streams', '-select_streams', 's', str(video_path)
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                data = json.loads(result.stdout)
                return data.get('streams', [])
            return []
        except (subprocess.TimeoutExpired, json.JSONDecodeError, Exception) as e:
            logger.error(f"获取字幕信息失败 {video_path.name}: {e}")
            return []

    def extract_subtitle(self, video_path: Path, stream_index: int, output_path: Path) -> bool:
        """提取指定的字幕流"""
        try:
            cmd = [
                'ffmpeg', '-i', str(video_path),
                '-map', f'0:s:{stream_index}',
                '-c:s', 'srt',  # 转换为SRT格式
                '-y',  # 覆盖输出文件
                str(output_path)
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            return result.returncode == 0

        except subprocess.TimeoutExpired:
            logger.error(f"字幕提取超时: {video_path.name}")
            return False
        except Exception as e:
            logger.error(f"字幕提取失败 {video_path.name}: {e}")
            return False

    def srt_to_dm(self, srt_path: Path, dm_path: Path, translate: bool = False, process_type: str = None) -> bool:
        """将SRT字幕转换为DM弹幕格式"""
        try:
            with open(srt_path, 'r', encoding='utf-8') as f:
                srt_content = f.read()

            # 如果启用了翻译或处理
            if self.translator and (translate or process_type):
                if translate:
                    logger.info(f"正在翻译字幕: {srt_path.name}")
                    srt_content = self.translator.process_subtitle_content(srt_content, "translate")
                elif process_type:
                    logger.info(f"正在处理字幕: {srt_path.name} - {process_type}")
                    srt_content = self.translator.process_subtitle_content(srt_content, process_type)

            # 解析SRT格式
            pattern = r'(\d+)\n(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})\n(.*?)(?=\n\d+\n|\n*$)'
            matches = re.findall(pattern, srt_content, re.DOTALL)

            dm_lines = []
            for match in matches:
                start_time = self.time_to_seconds(match[1])
                text = match[3].strip().replace('\n', ' ')

                # 清理文本
                text = re.sub(r'<[^>]+>', '', text)  # 移除HTML标签
                text = re.sub(r'\{[^}]+\}', '', text)  # 移除ASS样式标签
                text = text.strip()

                if text:  # 只添加非空文本
                    # DM格式: 时间,类型,字号,颜色,时间戳,弹幕池,用户ID,弹幕ID,内容
                    dm_line = f"{start_time:.3f},1,25,16777215,{int(start_time*1000)},0,user,0,{text}"
                    dm_lines.append(dm_line)

            with open(dm_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(dm_lines))

            return True

        except Exception as e:
            logger.error(f"SRT转DM失败 {srt_path.name}: {e}")
            return False

    def time_to_seconds(self, time_str: str) -> float:
        """将时间字符串转换为秒数"""
        # 格式: HH:MM:SS,mmm
        time_str = time_str.replace(',', '.')
        parts = time_str.split(':')
        hours = int(parts[0])
        minutes = int(parts[1])
        seconds = float(parts[2])
        return hours * 3600 + minutes * 60 + seconds

    def process_video(self, video_path: Path, translate: bool = False, process_type: str = None, keep_srt: bool = False) -> bool:
        """处理单个视频文件"""
        logger.info(f"处理视频: {video_path.name}")

        # 获取字幕信息
        subtitle_streams = self.get_subtitle_info(video_path)

        if not subtitle_streams:
            logger.warning(f"未找到字幕流: {video_path.name}")
            return False

        success = False
        for i, stream_info in enumerate(subtitle_streams):
            try:
                # 生成输出文件名
                base_name = video_path.stem

                # 根据语言和处理类型生成不同的文件名
                suffix = ""
                if translate:
                    suffix += "_translated"
                if process_type:
                    suffix += f"_{process_type}"

                srt_path = video_path.parent / f"{base_name}_sub{i}{suffix}.srt"
                dm_path = video_path.parent / f"{base_name}_sub{i}{suffix}.dm"

                # 提取字幕
                if self.extract_subtitle(video_path, i, srt_path):
                    logger.info(f"字幕提取成功: {srt_path.name}")

                    # 转换为DM格式（包含翻译/处理）
                    if self.srt_to_dm(srt_path, dm_path, translate, process_type):
                        logger.info(f"DM转换成功: {dm_path.name}")
                        success = True

                        # 根据设置决定是否保留SRT文件
                        if not keep_srt:
                            try:
                                srt_path.unlink()
                            except:
                                pass
                        else:
                            logger.info(f"SRT文件已保留: {srt_path.name}")
                    else:
                        logger.error(f"DM转换失败: {srt_path.name}")
                else:
                    logger.error(f"字幕提取失败: {video_path.name} 流{i}")

            except Exception as e:
                logger.error(f"处理字幕流{i}失败: {e}")

        return success

    def batch_process(self, translate: bool = False, process_type: str = None, keep_srt: bool = False) -> Dict[str, int]:
        """批量处理所有视频文件"""
        if not self.check_ffmpeg():
            logger.error("FFmpeg未安装或不可用，请先安装FFmpeg")
            return {'total': 0, 'success': 0, 'failed': 0}

        video_files = self.get_video_files()

        if not video_files:
            logger.info(f"在 {self.video_dir} 目录中未找到视频文件")
            return {'total': 0, 'success': 0, 'failed': 0}

        logger.info(f"找到 {len(video_files)} 个视频文件")

        if translate and not self.translator:
            logger.warning("翻译功能已请求但GLM API未配置")
            translate = False

        results = {'total': len(video_files), 'success': 0, 'failed': 0}

        for i, video_path in enumerate(video_files, 1):
            logger.info(f"进度: {i}/{len(video_files)}")

            if self.process_video(video_path, translate, process_type, keep_srt):
                results['success'] += 1
            else:
                results['failed'] += 1

        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='视频字幕提取器 - 支持GLM-4-Flash翻译')
    parser.add_argument('--dir', '-d', default='vidoin',
                       help='视频文件目录 (默认: vidoin)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')
    parser.add_argument('--openai-api-key',
                       help='GLM-4-Flash API密钥')
    parser.add_argument('--openai-base-url',
                       default='https://open.bigmodel.cn/api/paas/v4',
                       help='GLM-4-Flash API基础URL')
    parser.add_argument('--translate', action='store_true',
                       help='翻译字幕为中文')
    parser.add_argument('--process-type', choices=['clean', 'enhance'],
                       help='字幕处理类型: clean(清理) 或 enhance(增强)')
    parser.add_argument('--keep-srt', action='store_true',
                       help='保留SRT中间文件')
    parser.add_argument('--translate-table-text', action='store_true',
                       help='翻译表格文本（兼容参数）')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 创建提取器实例
    extractor = VideoSubtitleExtractor(
        video_dir=args.dir,
        glm_api_key=args.openai_api_key,
        glm_base_url=args.openai_base_url
    )

    # 批量处理
    results = extractor.batch_process(
        translate=args.translate,
        process_type=args.process_type,
        keep_srt=args.keep_srt
    )

    # 输出结果
    print(f"\n处理完成!")
    print(f"总文件数: {results['total']}")
    print(f"成功: {results['success']}")
    print(f"失败: {results['failed']}")

    if args.translate and args.openai_api_key:
        print(f"翻译功能: 已启用")
    if args.process_type:
        print(f"处理类型: {args.process_type}")

    if results['failed'] > 0:
        print(f"详细日志请查看: subtitle_extractor.log")

if __name__ == "__main__":
    main()
