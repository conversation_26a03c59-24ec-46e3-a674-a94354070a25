#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频字幕提取器
批量提取视频文件中的字幕并转换为.dm格式
支持GLM-4-Flash API翻译和处理
"""

import os
import sys
import subprocess
import json
import re
import requests
import time
import tempfile
import shutil
from pathlib import Path
from typing import List, Dict, Optional
import argparse
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('subtitle_extractor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AudioTranscriber:
    """音频转录器 - 支持多种转录方案"""

    def __init__(self, method: str = "whisper", api_key: str = None, base_url: str = None):
        self.method = method
        self.api_key = api_key
        self.base_url = base_url

        if method == "whisper":
            self.check_whisper()
        elif method == "glm" and api_key:
            self.headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

    def check_whisper(self) -> bool:
        """检查Whisper是否可用"""
        try:
            import whisper
            self.whisper = whisper
            logger.info("Whisper模块已加载")
            return True
        except ImportError:
            logger.warning("Whisper未安装，请运行: pip install openai-whisper")
            return False

    def extract_audio(self, video_path: Path, audio_path: Path) -> bool:
        """从视频中提取音频"""
        try:
            cmd = [
                'ffmpeg', '-i', str(video_path),
                '-vn',  # 不要视频
                '-acodec', 'pcm_s16le',  # 音频编码
                '-ar', '16000',  # 采样率
                '-ac', '1',  # 单声道
                '-y',  # 覆盖输出文件
                str(audio_path)
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            return result.returncode == 0

        except subprocess.TimeoutExpired:
            logger.error(f"音频提取超时: {video_path.name}")
            return False
        except Exception as e:
            logger.error(f"音频提取失败: {e}")
            return False

    def transcribe_with_whisper(self, audio_path: Path, language: str = None) -> str:
        """使用Whisper进行本地转录"""
        try:
            if not hasattr(self, 'whisper'):
                if not self.check_whisper():
                    return ""

            # 加载模型
            model = self.whisper.load_model("base")

            # 转录
            result = model.transcribe(str(audio_path), language=language)

            # 生成SRT格式
            srt_content = ""
            for i, segment in enumerate(result['segments'], 1):
                start_time = self.seconds_to_srt_time(segment['start'])
                end_time = self.seconds_to_srt_time(segment['end'])
                text = segment['text'].strip()

                srt_content += f"{i}\n{start_time} --> {end_time}\n{text}\n\n"

            return srt_content

        except Exception as e:
            logger.error(f"Whisper转录失败: {e}")
            return ""

    def transcribe_with_glm(self, audio_path: Path) -> str:
        """使用GLM API进行转录（需要先转换为文本）"""
        try:
            # 注意：GLM-4-Flash主要是文本模型，这里提供框架
            # 实际使用时可能需要其他音频转录API
            logger.warning("GLM音频转录功能需要专门的音频API")
            return ""

        except Exception as e:
            logger.error(f"GLM转录失败: {e}")
            return ""

    def seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

    def transcribe_video(self, video_path: Path, output_srt_path: Path, language: str = None) -> bool:
        """转录视频文件"""
        logger.info(f"开始转录视频: {video_path.name}")

        # 创建临时音频文件
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_audio:
            temp_audio_path = Path(temp_audio.name)

        try:
            # 提取音频
            if not self.extract_audio(video_path, temp_audio_path):
                logger.error(f"音频提取失败: {video_path.name}")
                return False

            # 转录
            if self.method == "whisper":
                srt_content = self.transcribe_with_whisper(temp_audio_path, language)
            elif self.method == "glm":
                srt_content = self.transcribe_with_glm(temp_audio_path)
            else:
                logger.error(f"不支持的转录方法: {self.method}")
                return False

            if not srt_content:
                logger.error(f"转录失败: {video_path.name}")
                return False

            # 保存SRT文件
            with open(output_srt_path, 'w', encoding='utf-8') as f:
                f.write(srt_content)

            logger.info(f"转录完成: {output_srt_path.name}")
            return True

        finally:
            # 清理临时文件
            try:
                temp_audio_path.unlink()
            except:
                pass

class GLMTranslator:
    """GLM-4-Flash API翻译器"""

    def __init__(self, api_key: str, base_url: str = "https://open.bigmodel.cn/api/paas/v4"):
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def translate_text(self, text: str, target_lang: str = "中文") -> str:
        """翻译文本"""
        try:
            url = f"{self.base_url}/chat/completions"

            prompt = f"请将以下字幕文本翻译成{target_lang}，保持原有的时间轴格式，只翻译文本内容：\n\n{text}"

            data = {
                "model": "GLM-4-Flash",
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 2000
            }

            response = requests.post(url, headers=self.headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result['choices'][0]['message']['content'].strip()

        except Exception as e:
            logger.error(f"翻译失败: {e}")
            return text  # 返回原文本

    def process_subtitle_content(self, content: str, operation: str = "translate") -> str:
        """处理字幕内容"""
        try:
            url = f"{self.base_url}/chat/completions"

            if operation == "translate":
                prompt = f"请将以下字幕翻译成中文，保持SRT格式不变：\n\n{content}"
            elif operation == "clean":
                prompt = f"请清理以下字幕内容，去除无关符号和噪音，保持SRT格式：\n\n{content}"
            elif operation == "enhance":
                prompt = f"请优化以下字幕的可读性，修正错误，保持SRT格式：\n\n{content}"
            else:
                return content

            data = {
                "model": "GLM-4-Flash",
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 4000
            }

            response = requests.post(url, headers=self.headers, json=data, timeout=60)
            response.raise_for_status()

            result = response.json()
            return result['choices'][0]['message']['content'].strip()

        except Exception as e:
            logger.error(f"字幕处理失败: {e}")
            return content

class VideoSubtitleExtractor:
    """视频字幕提取器类"""

    def __init__(self, video_dir: str = "vidoin", glm_api_key: str = None, glm_base_url: str = None,
                 transcribe_method: str = "whisper", enable_transcribe: bool = False):
        self.video_dir = Path(video_dir)
        self.supported_formats = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.m4v', '.webm', '.ts', '.mts'}
        self.subtitle_formats = {'.srt', '.ass', '.ssa', '.vtt', '.sub'}

        # GLM翻译器
        self.translator = None
        if glm_api_key:
            base_url = glm_base_url or "https://open.bigmodel.cn/api/paas/v4"
            self.translator = GLMTranslator(glm_api_key, base_url)
            logger.info("GLM-4-Flash翻译器已启用")

        # 转录器
        self.transcriber = None
        if enable_transcribe:
            self.transcriber = AudioTranscriber(
                method=transcribe_method,
                api_key=glm_api_key,
                base_url=glm_base_url
            )
            logger.info(f"转录器已启用: {transcribe_method}")

        # 确保目录存在
        self.video_dir.mkdir(exist_ok=True)

    def check_ffmpeg(self) -> bool:
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(['ffmpeg', '-version'],
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def get_video_files(self) -> List[Path]:
        """获取目录中的所有视频文件"""
        video_files = []
        for file_path in self.video_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                video_files.append(file_path)
        return sorted(video_files)

    def get_subtitle_info(self, video_path: Path) -> List[Dict]:
        """获取视频文件的字幕信息"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_streams', '-select_streams', 's', str(video_path)
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                data = json.loads(result.stdout)
                return data.get('streams', [])
            return []
        except (subprocess.TimeoutExpired, json.JSONDecodeError, Exception) as e:
            logger.error(f"获取字幕信息失败 {video_path.name}: {e}")
            return []

    def extract_subtitle(self, video_path: Path, stream_index: int, output_path: Path) -> bool:
        """提取指定的字幕流"""
        try:
            cmd = [
                'ffmpeg', '-i', str(video_path),
                '-map', f'0:s:{stream_index}',
                '-c:s', 'srt',  # 转换为SRT格式
                '-y',  # 覆盖输出文件
                str(output_path)
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            return result.returncode == 0

        except subprocess.TimeoutExpired:
            logger.error(f"字幕提取超时: {video_path.name}")
            return False
        except Exception as e:
            logger.error(f"字幕提取失败 {video_path.name}: {e}")
            return False

    def srt_to_dm(self, srt_path: Path, dm_path: Path, translate: bool = False, process_type: str = None) -> bool:
        """将SRT字幕转换为DM弹幕格式"""
        try:
            with open(srt_path, 'r', encoding='utf-8') as f:
                srt_content = f.read()

            # 如果启用了翻译或处理
            if self.translator and (translate or process_type):
                if translate:
                    logger.info(f"正在翻译字幕: {srt_path.name}")
                    srt_content = self.translator.process_subtitle_content(srt_content, "translate")
                elif process_type:
                    logger.info(f"正在处理字幕: {srt_path.name} - {process_type}")
                    srt_content = self.translator.process_subtitle_content(srt_content, process_type)

            # 解析SRT格式
            pattern = r'(\d+)\n(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})\n(.*?)(?=\n\d+\n|\n*$)'
            matches = re.findall(pattern, srt_content, re.DOTALL)

            dm_lines = []
            for match in matches:
                start_time = self.time_to_seconds(match[1])
                text = match[3].strip().replace('\n', ' ')

                # 清理文本
                text = re.sub(r'<[^>]+>', '', text)  # 移除HTML标签
                text = re.sub(r'\{[^}]+\}', '', text)  # 移除ASS样式标签
                text = text.strip()

                if text:  # 只添加非空文本
                    # DM格式: 时间,类型,字号,颜色,时间戳,弹幕池,用户ID,弹幕ID,内容
                    dm_line = f"{start_time:.3f},1,25,16777215,{int(start_time*1000)},0,user,0,{text}"
                    dm_lines.append(dm_line)

            with open(dm_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(dm_lines))

            return True

        except Exception as e:
            logger.error(f"SRT转DM失败 {srt_path.name}: {e}")
            return False

    def time_to_seconds(self, time_str: str) -> float:
        """将时间字符串转换为秒数"""
        # 格式: HH:MM:SS,mmm
        time_str = time_str.replace(',', '.')
        parts = time_str.split(':')
        hours = int(parts[0])
        minutes = int(parts[1])
        seconds = float(parts[2])
        return hours * 3600 + minutes * 60 + seconds

    def process_video(self, video_path: Path, translate: bool = False, process_type: str = None,
                     keep_srt: bool = False, transcribe_language: str = None) -> bool:
        """处理单个视频文件"""
        logger.info(f"处理视频: {video_path.name}")

        # 获取字幕信息
        subtitle_streams = self.get_subtitle_info(video_path)

        success = False

        # 如果有字幕流，提取现有字幕
        if subtitle_streams:
            logger.info(f"找到 {len(subtitle_streams)} 个字幕流")
            for i, stream_info in enumerate(subtitle_streams):
                try:
                    # 生成输出文件名
                    base_name = video_path.stem

                    # 根据语言和处理类型生成不同的文件名
                    suffix = ""
                    if translate:
                        suffix += "_translated"
                    if process_type:
                        suffix += f"_{process_type}"

                    srt_path = video_path.parent / f"{base_name}_sub{i}{suffix}.srt"
                    dm_path = video_path.parent / f"{base_name}_sub{i}{suffix}.dm"

                    # 提取字幕
                    if self.extract_subtitle(video_path, i, srt_path):
                        logger.info(f"字幕提取成功: {srt_path.name}")

                        # 转换为DM格式（包含翻译/处理）
                        if self.srt_to_dm(srt_path, dm_path, translate, process_type):
                            logger.info(f"DM转换成功: {dm_path.name}")
                            success = True

                            # 根据设置决定是否保留SRT文件
                            if not keep_srt:
                                try:
                                    srt_path.unlink()
                                except:
                                    pass
                            else:
                                logger.info(f"SRT文件已保留: {srt_path.name}")
                        else:
                            logger.error(f"DM转换失败: {srt_path.name}")
                    else:
                        logger.error(f"字幕提取失败: {video_path.name} 流{i}")

                except Exception as e:
                    logger.error(f"处理字幕流{i}失败: {e}")

        # 如果没有字幕流且启用了转录功能
        elif self.transcriber:
            logger.info(f"未找到字幕流，开始转录: {video_path.name}")
            try:
                base_name = video_path.stem

                # 根据处理类型生成文件名
                suffix = "_transcribed"
                if translate:
                    suffix += "_translated"
                if process_type:
                    suffix += f"_{process_type}"

                srt_path = video_path.parent / f"{base_name}{suffix}.srt"
                dm_path = video_path.parent / f"{base_name}{suffix}.dm"

                # 转录视频
                if self.transcriber.transcribe_video(video_path, srt_path, transcribe_language):
                    logger.info(f"转录成功: {srt_path.name}")

                    # 转换为DM格式（包含翻译/处理）
                    if self.srt_to_dm(srt_path, dm_path, translate, process_type):
                        logger.info(f"DM转换成功: {dm_path.name}")
                        success = True

                        # 根据设置决定是否保留SRT文件
                        if not keep_srt:
                            try:
                                srt_path.unlink()
                            except:
                                pass
                        else:
                            logger.info(f"SRT文件已保留: {srt_path.name}")
                    else:
                        logger.error(f"DM转换失败: {srt_path.name}")
                else:
                    logger.error(f"转录失败: {video_path.name}")

            except Exception as e:
                logger.error(f"转录处理失败: {e}")
        else:
            logger.warning(f"未找到字幕流且转录功能未启用: {video_path.name}")

        return success

    def batch_process(self, translate: bool = False, process_type: str = None,
                     keep_srt: bool = False, transcribe_language: str = None) -> Dict[str, int]:
        """批量处理所有视频文件"""
        if not self.check_ffmpeg():
            logger.error("FFmpeg未安装或不可用，请先安装FFmpeg")
            return {'total': 0, 'success': 0, 'failed': 0}

        video_files = self.get_video_files()

        if not video_files:
            logger.info(f"在 {self.video_dir} 目录中未找到视频文件")
            return {'total': 0, 'success': 0, 'failed': 0}

        logger.info(f"找到 {len(video_files)} 个视频文件")

        if translate and not self.translator:
            logger.warning("翻译功能已请求但GLM API未配置")
            translate = False

        results = {'total': len(video_files), 'success': 0, 'failed': 0, 'transcribed': 0}

        for i, video_path in enumerate(video_files, 1):
            logger.info(f"进度: {i}/{len(video_files)}")

            # 检查是否需要转录
            subtitle_streams = self.get_subtitle_info(video_path)
            if not subtitle_streams and self.transcriber:
                results['transcribed'] += 1

            if self.process_video(video_path, translate, process_type, keep_srt, transcribe_language):
                results['success'] += 1
            else:
                results['failed'] += 1

        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='视频字幕提取器 - 支持字幕提取、转录和GLM-4-Flash翻译')
    parser.add_argument('--dir', '-d', default='vidoin',
                       help='视频文件目录 (默认: vidoin)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')

    # GLM API参数
    parser.add_argument('--openai-api-key',
                       help='GLM-4-Flash API密钥')
    parser.add_argument('--openai-base-url',
                       default='https://open.bigmodel.cn/api/paas/v4',
                       help='GLM-4-Flash API基础URL')

    # 处理选项
    parser.add_argument('--translate', action='store_true',
                       help='翻译字幕为中文')
    parser.add_argument('--process-type', choices=['clean', 'enhance'],
                       help='字幕处理类型: clean(清理) 或 enhance(增强)')
    parser.add_argument('--keep-srt', action='store_true',
                       help='保留SRT中间文件')

    # 转录选项
    parser.add_argument('--enable-transcribe', action='store_true',
                       help='启用转录功能（用于无字幕视频）')
    parser.add_argument('--transcribe-method', choices=['whisper', 'glm'],
                       default='whisper',
                       help='转录方法: whisper(本地) 或 glm(API)')
    parser.add_argument('--transcribe-language',
                       help='转录语言代码 (如: zh, en, ja)')

    # 兼容参数
    parser.add_argument('--translate-table-text', action='store_true',
                       help='翻译表格文本（兼容参数）')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 创建提取器实例
    extractor = VideoSubtitleExtractor(
        video_dir=args.dir,
        glm_api_key=args.openai_api_key,
        glm_base_url=args.openai_base_url,
        transcribe_method=args.transcribe_method,
        enable_transcribe=args.enable_transcribe
    )

    # 批量处理
    results = extractor.batch_process(
        translate=args.translate,
        process_type=args.process_type,
        keep_srt=args.keep_srt,
        transcribe_language=args.transcribe_language
    )

    # 输出结果
    print(f"\n处理完成!")
    print(f"总文件数: {results['total']}")
    print(f"成功: {results['success']}")
    print(f"失败: {results['failed']}")

    if 'transcribed' in results and results['transcribed'] > 0:
        print(f"转录文件数: {results['transcribed']}")

    if args.translate and args.openai_api_key:
        print(f"翻译功能: 已启用")
    if args.process_type:
        print(f"处理类型: {args.process_type}")
    if args.enable_transcribe:
        print(f"转录功能: 已启用 ({args.transcribe_method})")

    if results['failed'] > 0:
        print(f"详细日志请查看: subtitle_extractor.log")

if __name__ == "__main__":
    main()
