#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信公众号文章爬虫命令行工具
支持自定义URL和输出目录，支持分批爬取和跳过功能
"""

import argparse
import sys
import os
from batch_wechat_crawler_with_images import BatchWeChatCrawlerWithImages

def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(
        description='微信公众号文章爬虫 - 支持分批爬取和跳过功能',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本用法
  python wechat_crawler_cli.py -u "https://mp.weixin.qq.com/s/qJN0eQliHRFl2WbIt4czSg"

  # 分批爬取示例
  python wechat_crawler_cli.py -u "https://mp.weixin.qq.com/s/example" -n 10        # 第1-10篇
  python wechat_crawler_cli.py -u "https://mp.weixin.qq.com/s/example" -n 10 --skip 10  # 第11-20篇
  python wechat_crawler_cli.py -u "https://mp.weixin.qq.com/s/example" -n 10 --skip 20  # 第21-30篇

  # 查看历史记录
  python wechat_crawler_cli.py --show-history

  # 清除历史记录
  python wechat_crawler_cli.py --clear-history
        """
    )

    parser.add_argument(
        '-u', '--url',
        help='要爬取的微信文章URL'
    )

    parser.add_argument(
        '-n', '--num-articles',
        type=int,
        default=10,
        help='本批次要爬取的文章数量 (默认: 10)'
    )

    parser.add_argument(
        '--skip',
        type=int,
        default=0,
        help='跳过的文章数量 (默认: 0)'
    )

    parser.add_argument(
        '-o', '--output-dir',
        default="E:/mcp-test/obsidian wang/微信好文",
        help='输出目录 (默认: E:/mcp-test/obsidian wang/微信好文)'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细输出'
    )

    parser.add_argument(
        '--show-history',
        action='store_true',
        help='显示分批爬取历史记录'
    )

    parser.add_argument(
        '--clear-history',
        action='store_true',
        help='清除历史记录'
    )

    args = parser.parse_args()

    # 创建爬虫实例
    crawler = BatchWeChatCrawlerWithImages(output_dir=args.output_dir)

    # 显示历史记录
    if args.show_history:
        crawler.show_batch_history()
        return

    # 清除历史记录
    if args.clear_history:
        crawler.clear_history()
        return

    # 验证必需参数
    if not args.url:
        print("❌ 错误: 必须提供起始文章URL")
        print("💡 使用 --show-history 查看历史记录")
        print("💡 使用 --clear-history 清除历史记录")
        sys.exit(1)

    # 验证URL
    if not args.url.startswith('http'):
        print("❌ 错误: URL必须以http或https开头")
        sys.exit(1)

    if 'mp.weixin.qq.com' not in args.url:
        print("❌ 错误: 请提供有效的微信公众号文章URL")
        sys.exit(1)

    # 显示配置信息
    print("🚀 分批微信公众号文章爬虫启动...")
    print(f"📄 起始URL: {args.url}")
    print(f"🎯 本批目标数: {args.num_articles}")
    print(f"⏭️  跳过文章数: {args.skip}")
    print(f"📁 输出目录: {args.output_dir}")
    print("-" * 80)

    try:
        # 开始分批爬取
        saved_files = crawler.crawl_articles_batch(args.url, args.num_articles, args.skip)

        # 显示结果
        print("\n" + "="*80)
        if saved_files:
            print("🎉 分批爬取任务完成！")
            print(f"📊 本批成功爬取: {len(saved_files)} 篇文章")
            print(f"📁 保存目录: {crawler.output_dir}")

            # 显示图片统计
            batch_articles = crawler.articles[-len(saved_files):] if saved_files else []
            total_images = sum(article.get('image_count', 0) for article in batch_articles)
            print(f"🖼️  本批下载图片: {total_images} 张")
            print(f"�️  图片目录: {crawler.images_dir}")

            # 显示批次统计
            print(f"\n� 累计统计:")
            print(f"   📚 总批次数: {crawler.batch_stats['total_batches']}")
            print(f"   � 累计文章: {crawler.batch_stats['total_articles']} 篇")
            print(f"   🖼️  累计图片: {crawler.batch_stats['total_images']} 张")

            # 显示文章列表
            if args.verbose and batch_articles:
                print("\n📚 本批爬取的文章列表:")
                for i, article in enumerate(batch_articles, 1):
                    image_info = ""
                    if article.get('image_count', 0) > 0:
                        image_info = f" (含 {article['image_count']} 张图片)"
                    print(f"  {i:2d}. {article['title']}{image_info}")

            # 提供下一批次的命令
            next_skip = args.skip + len(saved_files)
            print(f"\n💡 继续爬取下一批次:")
            print(f"   python wechat_crawler_cli.py -u \"{args.url}\" -n {args.num_articles} --skip {next_skip}")

        else:
            print("❌ 本批次爬取失败或无新文章")
            print("\n💡 可能的原因:")
            print("   - 所有文章都已爬取过")
            print("   - 跳过数量超过了可用文章数")
            print("   - 网络连接问题")
            print("\n💡 建议:")
            print("   1. 检查历史记录: --show-history")
            print("   2. 尝试不同的起始URL")
            print("   3. 减少跳过数量")

        print("="*80)

    except KeyboardInterrupt:
        print("\n⚠️  用户中断了爬取过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 爬取过程中出现错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
