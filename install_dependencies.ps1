# PowerShell版本的依赖安装脚本
# 视频字幕提取器 - 依赖安装

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "视频字幕提取器 - 依赖安装" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Python是否安装
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Python已安装: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python未找到"
    }
} catch {
    Write-Host "✗ 错误: 未找到Python，请先安装Python" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "正在安装Python依赖包..." -ForegroundColor Yellow
Write-Host ""

# 安装基础依赖
Write-Host "安装基础依赖..." -ForegroundColor White
try {
    pip install requests pathlib argparse
    Write-Host "✓ 基础依赖安装完成" -ForegroundColor Green
} catch {
    Write-Host "⚠ 基础依赖安装可能有问题" -ForegroundColor Yellow
}

Write-Host ""

# 安装Whisper（用于转录）
Write-Host "安装Whisper转录依赖..." -ForegroundColor White
try {
    pip install openai-whisper
    Write-Host "✓ Whisper安装完成" -ForegroundColor Green
} catch {
    Write-Host "✗ Whisper安装失败" -ForegroundColor Red
}

Write-Host ""

# 安装音频处理依赖
Write-Host "安装音频处理依赖..." -ForegroundColor White
try {
    pip install torch torchaudio
    Write-Host "✓ 音频处理依赖安装完成" -ForegroundColor Green
} catch {
    Write-Host "⚠ 音频处理依赖安装可能有问题" -ForegroundColor Yellow
}

Write-Host ""

# 安装其他依赖
Write-Host "安装其他依赖..." -ForegroundColor White
try {
    pip install numpy scipy
    Write-Host "✓ 其他依赖安装完成" -ForegroundColor Green
} catch {
    Write-Host "⚠ 其他依赖安装可能有问题" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "检查FFmpeg安装状态" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 检查FFmpeg是否安装
try {
    $ffmpegVersion = ffmpeg -version 2>$null | Select-Object -First 1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ FFmpeg已安装并可用" -ForegroundColor Green
        Write-Host $ffmpegVersion -ForegroundColor Gray
    } else {
        throw "FFmpeg未找到"
    }
} catch {
    Write-Host ""
    Write-Host "⚠ 警告: 未找到FFmpeg" -ForegroundColor Yellow
    Write-Host "FFmpeg是必需的依赖，用于视频和音频处理" -ForegroundColor White
    Write-Host ""
    Write-Host "请按照以下步骤安装FFmpeg:" -ForegroundColor White
    Write-Host "1. 访问 https://ffmpeg.org/download.html" -ForegroundColor Gray
    Write-Host "2. 下载Windows版本" -ForegroundColor Gray
    Write-Host "3. 解压到任意目录（如 C:\ffmpeg）" -ForegroundColor Gray
    Write-Host "4. 将 C:\ffmpeg\bin 添加到系统PATH环境变量" -ForegroundColor Gray
    Write-Host "5. 重启PowerShell" -ForegroundColor Gray
    Write-Host ""
    Write-Host "或者使用包管理器安装:" -ForegroundColor White
    Write-Host "- Chocolatey: choco install ffmpeg" -ForegroundColor Gray
    Write-Host "- winget: winget install ffmpeg" -ForegroundColor Gray
    Write-Host "- Scoop: scoop install ffmpeg" -ForegroundColor Gray
    Write-Host ""
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "安装完成" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "依赖安装完成！" -ForegroundColor Green
Write-Host ""
Write-Host "使用方法:" -ForegroundColor White
Write-Host "1. 基本字幕提取: python video_subtitle_extractor.py" -ForegroundColor Gray
Write-Host "2. 启用转录功能: python video_subtitle_extractor.py --enable-transcribe" -ForegroundColor Gray
Write-Host "3. 智能模式: .\start.ps1" -ForegroundColor Gray
Write-Host "4. 交互式界面: .\extract_subtitles.ps1" -ForegroundColor Gray
Write-Host ""
Read-Host "按任意键退出"
