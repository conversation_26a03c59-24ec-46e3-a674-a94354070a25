#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
支持图片下载的微信公众号文章爬虫
"""

import requests
import re
import os
import time
import hashlib
from datetime import datetime
from typing import List, Dict, Set, Tuple
from urllib.parse import urljoin, urlparse, quote
from bs4 import BeautifulSoup
import mimetypes

class WeChatCrawlerWithImages:
    def __init__(self, output_dir: str = "E:/mcp-test/obsidian wang/微信好文"):
        """
        初始化支持图片的微信文章爬虫

        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        self.images_dir = os.path.join(output_dir, "images")
        self.crawled_urls: Set[str] = set()
        self.articles: List[Dict] = []
        self.account_info = {}
        self.downloaded_images: Dict[str, str] = {}  # URL -> 本地路径映射

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(self.images_dir, exist_ok=True)

        # 设置请求头，模拟浏览器
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://mp.weixin.qq.com/',
        }

        # 图片请求头
        self.image_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://mp.weixin.qq.com/',
        }

        # 创建session
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def clean_filename(self, filename: str) -> str:
        """清理文件名，移除非法字符"""
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'\s+', ' ', filename)
        if len(filename) > 100:
            filename = filename[:100]
        return filename.strip()

    def get_image_extension(self, url: str, content_type: str = None) -> str:
        """
        获取图片扩展名

        Args:
            url: 图片URL
            content_type: 内容类型

        Returns:
            文件扩展名
        """
        # 首先尝试从URL获取扩展名
        parsed_url = urlparse(url)
        path = parsed_url.path
        if '.' in path:
            ext = os.path.splitext(path)[1].lower()
            if ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']:
                return ext

        # 从content-type获取扩展名
        if content_type:
            ext = mimetypes.guess_extension(content_type.split(';')[0])
            if ext:
                return ext.lower()

        # 默认使用jpg
        return '.jpg'

    def download_image(self, img_url: str, article_title: str) -> str:
        """
        下载单张图片

        Args:
            img_url: 图片URL
            article_title: 文章标题（用于创建子目录）

        Returns:
            本地图片路径（相对于输出目录）
        """
        try:
            # 如果已经下载过，直接返回路径
            if img_url in self.downloaded_images:
                return self.downloaded_images[img_url]

            print(f"🖼️  下载图片: {img_url}")

            # 发送请求下载图片
            response = self.session.get(img_url, headers=self.image_headers, timeout=30)
            response.raise_for_status()

            # 获取内容类型
            content_type = response.headers.get('content-type', '')

            # 生成文件名
            url_hash = hashlib.md5(img_url.encode()).hexdigest()[:8]
            ext = self.get_image_extension(img_url, content_type)

            # 创建文章专用的图片目录
            article_dir = self.clean_filename(article_title)
            article_images_dir = os.path.join(self.images_dir, article_dir)
            os.makedirs(article_images_dir, exist_ok=True)

            # 生成完整的文件路径
            filename = f"img_{url_hash}{ext}"
            full_path = os.path.join(article_images_dir, filename)

            # 保存图片
            with open(full_path, 'wb') as f:
                f.write(response.content)

            # 生成相对路径（相对于输出目录）
            relative_path = os.path.join("images", article_dir, filename).replace("\\", "/")

            # 记录下载的图片
            self.downloaded_images[img_url] = relative_path

            print(f"✅ 图片已保存: {relative_path}")
            return relative_path

        except Exception as e:
            print(f"❌ 下载图片失败 {img_url}: {str(e)}")
            return img_url  # 返回原URL作为fallback

    def extract_and_download_images(self, soup: BeautifulSoup, article_title: str) -> Dict[str, str]:
        """
        提取并下载文章中的所有图片

        Args:
            soup: BeautifulSoup对象
            article_title: 文章标题

        Returns:
            图片URL到本地路径的映射
        """
        image_mapping = {}

        # 查找所有图片标签
        img_tags = soup.find_all('img')

        print(f"🔍 发现 {len(img_tags)} 张图片")

        for img in img_tags:
            # 获取图片URL
            img_url = img.get('src') or img.get('data-src') or img.get('data-original')

            if not img_url:
                continue

            # 处理相对URL
            if img_url.startswith('//'):
                img_url = 'https:' + img_url
            elif img_url.startswith('/'):
                img_url = 'https://mp.weixin.qq.com' + img_url

            # 过滤掉一些不需要的图片（如表情、小图标等）
            if any(skip in img_url.lower() for skip in ['emoji', 'icon', 'avatar']):
                continue

            # 下载图片
            local_path = self.download_image(img_url, article_title)
            image_mapping[img_url] = local_path

            # 添加延迟避免请求过快
            time.sleep(0.5)

        return image_mapping

    def html_to_markdown_with_images(self, html_content: str, article_title: str) -> Tuple[str, int]:
        """
        将HTML转换为Markdown并下载图片

        Args:
            html_content: HTML内容
            article_title: 文章标题

        Returns:
            (Markdown内容, 下载的图片数量)
        """
        soup = BeautifulSoup(html_content, 'html.parser')

        # 移除script和style标签
        for script in soup(["script", "style"]):
            script.decompose()

        # 提取并下载图片
        image_mapping = self.extract_and_download_images(soup, article_title)

        # 替换图片URL为本地路径
        for img in soup.find_all('img'):
            img_url = img.get('src') or img.get('data-src') or img.get('data-original')
            if img_url and img_url in image_mapping:
                local_path = image_mapping[img_url]
                # 创建Markdown图片语法
                alt_text = img.get('alt', '图片')
                img_markdown = f"![{alt_text}]({local_path})"

                # 替换img标签
                img.replace_with(img_markdown)

        # 获取纯文本并清理
        text = soup.get_text()
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = '\n'.join(chunk for chunk in chunks if chunk)

        return text, len(image_mapping)

    def extract_account_info(self, html_content: str) -> Dict:
        """提取公众号信息"""
        soup = BeautifulSoup(html_content, 'html.parser')
        account_info = {}

        # 提取公众号名称
        account_selectors = [
            '#js_name',
            '.profile_nickname',
            '.rich_media_meta_text'
        ]

        for selector in account_selectors:
            elem = soup.select_one(selector)
            if elem:
                account_info['name'] = elem.get_text().strip()
                break

        # 从URL中提取biz参数
        biz_match = re.search(r'__biz=([^&]+)', html_content)
        if biz_match:
            account_info['biz'] = biz_match.group(1)

        return account_info

    def clean_url(self, url: str) -> str:
        """清理URL，移除HTML标签和多余字符"""
        if not url:
            return ""

        # 移除HTML标签
        url = re.sub(r'<[^>]+>', '', url)

        # 移除引号和其他特殊字符
        url = re.sub(r'["\'>].*$', '', url)

        # 移除URL中的HTML实体
        url = url.replace('&amp;', '&')

        # 确保URL格式正确
        if url.startswith('//'):
            url = 'https:' + url
        elif url.startswith('/'):
            url = 'https://mp.weixin.qq.com' + url

        return url.strip()

    def extract_article_links(self, html_content: str, base_url: str) -> List[str]:
        """从HTML内容中提取文章链接"""
        soup = BeautifulSoup(html_content, 'html.parser')
        links = []

        # 查找微信文章链接
        link_patterns = [
            'a[href*="mp.weixin.qq.com/s"]',
        ]

        for pattern in link_patterns:
            found_links = soup.select(pattern)
            for link in found_links:
                href = link.get('href')
                if href:
                    # 清理URL
                    clean_href = self.clean_url(href)
                    if clean_href and 'mp.weixin.qq.com/s' in clean_href:
                        full_url = urljoin(base_url, clean_href)
                        if full_url not in self.crawled_urls:
                            links.append(full_url)

        # 从文本中提取链接，使用更严格的正则表达式
        url_patterns = [
            r'https?://mp\.weixin\.qq\.com/s/[a-zA-Z0-9_-]+',
            r'http://mp\.weixin\.qq\.com/s\?[^"\s<>]+',
        ]

        for pattern in url_patterns:
            text_urls = re.findall(pattern, html_content)
            for url in text_urls:
                clean_url = self.clean_url(url)
                if clean_url and clean_url not in self.crawled_urls:
                    links.append(clean_url)

        return list(set(links))

    def search_account_articles(self, account_name: str, biz: str = None, max_results: int = 20) -> List[str]:
        """
        增强版搜索指定公众号的文章

        Args:
            account_name: 公众号名称
            biz: 公众号biz参数
            max_results: 最大结果数

        Returns:
            文章链接列表
        """
        links = []

        try:
            # 多种搜索策略
            search_strategies = [
                self._search_via_baidu,
                self._search_via_sogou,
                self._search_via_bing,
                self._search_via_wechat_api,
            ]

            for strategy in search_strategies:
                if len(links) >= max_results:
                    break

                try:
                    strategy_links = strategy(account_name, biz, max_results - len(links))
                    for link in strategy_links:
                        if link not in links and link not in self.crawled_urls:
                            links.append(link)
                            if len(links) >= max_results:
                                break
                except Exception as e:
                    print(f"搜索策略失败: {str(e)}")
                    continue

            print(f"🎯 总共搜索到 {len(links)} 个相关链接")

        except Exception as e:
            print(f"搜索账号文章时出错: {str(e)}")

        return links

    def _search_via_baidu(self, account_name: str, biz: str = None, max_results: int = 20) -> List[str]:
        """通过百度搜索"""
        links = []
        search_queries = [
            f'site:mp.weixin.qq.com "{account_name}"',
            f'site:mp.weixin.qq.com "{account_name}" 项目',
            f'site:mp.weixin.qq.com "{account_name}" 管理',
            f'site:mp.weixin.qq.com "{account_name}" 洞见',
            f'site:mp.weixin.qq.com "{account_name}" 分享',
        ]

        if biz:
            search_queries.append(f'site:mp.weixin.qq.com __biz={biz}')

        for query in search_queries[:3]:  # 限制查询数量
            if len(links) >= max_results:
                break

            print(f"🔍 百度搜索: {query}")
            search_url = f"https://www.baidu.com/s?wd={quote(query)}&pn=0"

            try:
                response = self.session.get(search_url, timeout=10)
                response.raise_for_status()
                soup = BeautifulSoup(response.text, 'html.parser')

                # 提取搜索结果
                for link in soup.find_all('a', href=True):
                    href = link.get('href', '')
                    if 'mp.weixin.qq.com/s' in href and 'baidu.com' not in href:
                        clean_href = self.clean_url(href)
                        if self.validate_url(clean_href) and clean_href not in links:
                            links.append(clean_href)
                            if len(links) >= max_results:
                                break

                time.sleep(2)
            except Exception as e:
                print(f"百度搜索出错: {str(e)}")

        return links

    def _search_via_sogou(self, account_name: str, biz: str = None, max_results: int = 20) -> List[str]:
        """通过搜狗微信搜索"""
        links = []
        print(f"🔍 搜狗微信搜索: {account_name}")

        try:
            # 搜狗微信搜索URL
            search_url = f"https://weixin.sogou.com/weixin?type=1&query={quote(account_name)}"

            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找文章链接
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if 'mp.weixin.qq.com/s' in href:
                    clean_href = self.clean_url(href)
                    if self.validate_url(clean_href) and clean_href not in links:
                        links.append(clean_href)
                        if len(links) >= max_results:
                            break

            time.sleep(3)  # 搜狗需要更长间隔
        except Exception as e:
            print(f"搜狗搜索出错: {str(e)}")

        return links

    def _search_via_bing(self, account_name: str, biz: str = None, max_results: int = 20) -> List[str]:
        """通过必应搜索"""
        links = []
        query = f'site:mp.weixin.qq.com "{account_name}"'
        print(f"🔍 必应搜索: {query}")

        try:
            search_url = f"https://www.bing.com/search?q={quote(query)}"

            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')

            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if 'mp.weixin.qq.com/s' in href:
                    clean_href = self.clean_url(href)
                    if self.validate_url(clean_href) and clean_href not in links:
                        links.append(clean_href)
                        if len(links) >= max_results:
                            break

            time.sleep(2)
        except Exception as e:
            print(f"必应搜索出错: {str(e)}")

        return links

    def _search_via_wechat_api(self, account_name: str, biz: str = None, max_results: int = 20) -> List[str]:
        """尝试通过微信相关API搜索"""
        links = []

        if not biz:
            return links

        print(f"🔍 微信API搜索: {account_name}")

        try:
            # 构建可能的文章URL模式
            base_patterns = [
                f"https://mp.weixin.qq.com/s?__biz={biz}&mid=",
                f"https://mp.weixin.qq.com/s?__biz={biz}&tempkey=",
            ]

            # 尝试一些常见的mid值范围
            mid_ranges = [
                range(**********, **********),  # 最近的文章
                range(**********, **********),  # 稍早的文章
            ]

            for pattern in base_patterns:
                for mid_range in mid_ranges:
                    for mid in mid_range:
                        if len(links) >= max_results:
                            break

                        test_url = f"{pattern}{mid}&idx=1&sn=test"
                        # 这里只是构建URL模式，实际需要有效的sn参数
                        # 在实际应用中，这种方法成功率较低

            time.sleep(1)
        except Exception as e:
            print(f"微信API搜索出错: {str(e)}")

        return links

    def extract_urls_from_clipboard(self) -> List[str]:
        """从剪贴板提取微信文章URL"""
        try:
            import pyperclip
            clipboard_content = pyperclip.paste()
            return self.extract_urls_from_text(clipboard_content)
        except ImportError:
            print("❌ 需要安装pyperclip: pip install pyperclip")
            return []
        except Exception as e:
            print(f"❌ 读取剪贴板失败: {str(e)}")
            return []

    def extract_urls_from_text(self, text: str) -> List[str]:
        """从文本中提取微信文章URL"""
        if not text:
            return []

        # 多种URL模式
        patterns = [
            r'https://mp\.weixin\.qq\.com/s/[A-Za-z0-9_-]+',
            r'https://mp\.weixin\.qq\.com/s\?[^"\s<>]+',
            r'http://mp\.weixin\.qq\.com/s/[A-Za-z0-9_-]+',
            r'http://mp\.weixin\.qq\.com/s\?[^"\s<>]+',
            r'https://mp\.weixin\.qq\.com/[^"\s<>]*',
        ]

        urls = set()
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                clean_url = self.clean_url(match)
                if self.validate_url(clean_url):
                    urls.add(clean_url)

        return list(urls)

    def extract_urls_from_file(self, file_path: str) -> List[str]:
        """从文件中提取微信文章URL"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return self.extract_urls_from_text(content)
        except Exception as e:
            print(f"❌ 读取文件失败 {file_path}: {str(e)}")
            return []

    def extract_urls_from_browser_history(self) -> List[str]:
        """从浏览器历史记录中提取微信文章URL（仅Chrome）"""
        urls = []
        try:
            import sqlite3
            import os
            from pathlib import Path

            # Chrome历史记录路径
            chrome_paths = [
                Path.home() / "AppData/Local/Google/Chrome/User Data/Default/History",
                Path.home() / "Library/Application Support/Google/Chrome/Default/History",
                Path.home() / "/.config/google-chrome/Default/History",
            ]

            for chrome_path in chrome_paths:
                if chrome_path.exists():
                    print(f"🔍 检查Chrome历史记录: {chrome_path}")

                    # 复制数据库文件（避免锁定）
                    temp_path = chrome_path.parent / "History_temp"
                    import shutil
                    shutil.copy2(chrome_path, temp_path)

                    try:
                        conn = sqlite3.connect(temp_path)
                        cursor = conn.cursor()

                        # 查询微信文章URL
                        cursor.execute("""
                            SELECT url FROM urls
                            WHERE url LIKE '%mp.weixin.qq.com/s%'
                            ORDER BY last_visit_time DESC
                            LIMIT 100
                        """)

                        for row in cursor.fetchall():
                            url = row[0]
                            clean_url = self.clean_url(url)
                            if self.validate_url(clean_url) and clean_url not in urls:
                                urls.append(clean_url)

                        conn.close()

                    finally:
                        # 删除临时文件
                        if temp_path.exists():
                            temp_path.unlink()

                    break

            print(f"🎯 从浏览器历史记录找到 {len(urls)} 个微信文章URL")

        except Exception as e:
            print(f"❌ 读取浏览器历史记录失败: {str(e)}")

        return urls

    def discover_related_articles_advanced(self, article_content: str, account_name: str) -> List[str]:
        """高级相关文章发现"""
        urls = []

        try:
            # 从文章内容中提取关键词
            keywords = self._extract_keywords(article_content, account_name)

            # 使用关键词进行搜索
            for keyword in keywords[:5]:  # 限制关键词数量
                search_urls = self._search_by_keyword(keyword, account_name)
                urls.extend(search_urls)

                if len(urls) >= 20:  # 限制发现的URL数量
                    break

            # 去重
            unique_urls = []
            for url in urls:
                if url not in unique_urls and url not in self.crawled_urls:
                    unique_urls.append(url)

            print(f"🎯 通过关键词发现 {len(unique_urls)} 个相关文章")

        except Exception as e:
            print(f"❌ 高级文章发现失败: {str(e)}")

        return unique_urls[:10]  # 返回前10个

    def _extract_keywords(self, content: str, account_name: str) -> List[str]:
        """从文章内容中提取关键词"""
        keywords = []

        # 简单的关键词提取
        common_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}

        # 分词（简单版本）
        words = re.findall(r'[\u4e00-\u9fff]+', content)
        word_freq = {}

        for word in words:
            if len(word) >= 2 and word not in common_words:
                word_freq[word] = word_freq.get(word, 0) + 1

        # 按频率排序
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        keywords = [word for word, freq in sorted_words[:10] if freq >= 2]

        # 添加账号名称相关关键词
        if account_name:
            keywords.insert(0, account_name)

        return keywords

    def _search_by_keyword(self, keyword: str, account_name: str) -> List[str]:
        """通过关键词搜索相关文章"""
        urls = []

        try:
            query = f'site:mp.weixin.qq.com "{account_name}" "{keyword}"'
            search_url = f"https://www.baidu.com/s?wd={quote(query)}"

            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')

            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if 'mp.weixin.qq.com/s' in href:
                    clean_href = self.clean_url(href)
                    if self.validate_url(clean_href):
                        urls.append(clean_href)
                        if len(urls) >= 5:  # 每个关键词最多5个结果
                            break

            time.sleep(1)

        except Exception as e:
            print(f"❌ 关键词搜索失败 {keyword}: {str(e)}")

        return urls

    def validate_url(self, url: str) -> bool:
        """验证URL是否有效"""
        if not url:
            return False

        # 检查URL格式
        if not url.startswith(('http://', 'https://')):
            return False

        # 检查是否是微信文章URL
        if 'mp.weixin.qq.com' not in url:
            return False

        # 检查URL中是否包含HTML标签
        if '<' in url or '>' in url:
            return False

        return True

    def crawl_single_article(self, url: str) -> Dict:
        """爬取单篇文章（包含图片）"""
        # 清理和验证URL
        clean_url = self.clean_url(url)

        if not self.validate_url(clean_url):
            print(f"❌ 无效的URL: {url}")
            return None

        print(f"📄 正在爬取文章: {clean_url}")

        try:
            response = self.session.get(clean_url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')

            # 如果是第一篇文章，提取公众号信息
            if not self.account_info:
                self.account_info = self.extract_account_info(response.text)
                print(f"📱 检测到公众号: {self.account_info.get('name', '未知')}")

            # 提取标题
            title = ""
            title_selectors = [
                'h1.rich_media_title',
                'h1#activity-name',
                'h1',
                '.rich_media_title',
                '#activity-name',
                'title'
            ]

            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    title = title_elem.get_text().strip()
                    if title and len(title) > 5:
                        break

            if not title:
                title = f"文章_{int(time.time())}"

            # 提取作者
            author = ""
            author_selectors = [
                '.rich_media_meta_text',
                '.profile_nickname',
                '#js_name',
                '.account_nickname'
            ]

            for selector in author_selectors:
                author_elem = soup.select_one(selector)
                if author_elem:
                    author = author_elem.get_text().strip()
                    if author:
                        break

            if not author and self.account_info.get('name'):
                author = self.account_info['name']

            # 提取发布时间
            publish_time = ""
            time_selectors = [
                '#publish_time',
                '.rich_media_meta_text',
                '[id*="time"]',
                '.time',
                '.rich_media_meta_list em',
                '.rich_media_meta em'
            ]

            for selector in time_selectors:
                time_elem = soup.select_one(selector)
                if time_elem:
                    time_text = time_elem.get_text().strip()
                    # 更严格的时间格式检查
                    if re.search(r'\d{4}[-年]\d{1,2}[-月]\d{1,2}', time_text) or re.search(r'\d{4}-\d{2}-\d{2}', time_text):
                        publish_time = time_text
                        break

            # 如果还没找到时间，尝试从script标签中提取
            if not publish_time:
                scripts = soup.find_all('script')
                for script in scripts:
                    if script.string:
                        # 查找时间戳或日期
                        time_patterns = [
                            r'"publish_time"[:\s]*"?(\d{4}-\d{2}-\d{2}[^"]*)"?',
                            r'(\d{4}年\d{1,2}月\d{1,2}日)',
                            r'"ct"[:\s]*"?(\d{4}-\d{2}-\d{2}[^"]*)"?',
                            r'createTime["\s:]*"?(\d{4}-\d{2}-\d{2}[^"]*)"?',
                            r'publish_time["\s:]*(\d{10})',  # 时间戳
                        ]

                        for pattern in time_patterns:
                            time_match = re.search(pattern, script.string)
                            if time_match:
                                time_str = time_match.group(1)
                                # 如果是时间戳，转换为日期
                                if time_str.isdigit() and len(time_str) == 10:
                                    try:
                                        import datetime as dt
                                        publish_time = dt.datetime.fromtimestamp(int(time_str)).strftime('%Y-%m-%d')
                                    except:
                                        publish_time = time_str
                                else:
                                    publish_time = time_str
                                break

                        if publish_time:
                            break

            # 如果仍然没有找到时间，使用当前日期
            if not publish_time:
                publish_time = datetime.now().strftime('%Y-%m-%d')

            # 提取正文内容
            content_html = ""
            content_selectors = [
                '#js_content',
                '.rich_media_content',
                '.content',
                'article'
            ]

            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    content_html = str(content_elem)
                    break

            if not content_html:
                content_html = response.text

            # 转换为Markdown并下载图片
            markdown_content, image_count = self.html_to_markdown_with_images(content_html, title)

            article_info = {
                'title': title,
                'author': author,
                'publish_time': publish_time,
                'url': clean_url,  # 使用清理后的URL
                'content': markdown_content,
                'image_count': image_count,
                'crawl_time': datetime.now().isoformat()
            }

            # 提取文章中的其他链接
            article_links = self.extract_article_links(response.text, clean_url)
            article_info['found_links'] = article_links

            self.crawled_urls.add(clean_url)
            print(f"✅ 成功爬取: {title} (包含 {image_count} 张图片)")

            return article_info

        except Exception as e:
            print(f"❌ 爬取文章时出错 {url}: {str(e)}")
            return None

    def save_article_to_markdown(self, article: Dict) -> str:
        """将文章保存为Markdown文件"""
        if not article:
            return None

        title = self.clean_filename(article['title'])
        filename = f"{title}.md"
        filepath = os.path.join(self.output_dir, filename)

        # 避免文件名冲突
        counter = 1
        while os.path.exists(filepath):
            filename = f"{title}_{counter}.md"
            filepath = os.path.join(self.output_dir, filename)
            counter += 1

        # 生成Markdown内容
        image_info = f"**包含图片:** {article['image_count']} 张  " if article.get('image_count', 0) > 0 else ""

        markdown_content = f"""# {article['title']}

**作者:** {article['author']}
**发布时间:** {article['publish_time']}
{image_info}**原文链接:** {article['url']}
**爬取时间:** {article['crawl_time']}

---

{article['content']}

---

*本文由微信文章爬虫自动抓取并转换为Markdown格式*
"""

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            print(f"💾 文章已保存: {os.path.basename(filepath)}")
            return filepath
        except Exception as e:
            print(f"❌ 保存文章失败 {filepath}: {str(e)}")
            return None

    def crawl_articles(self, start_url: str, max_articles: int = 46) -> List[str]:
        """爬取文章（包括起始文章和相关文章）"""
        print(f"🚀 开始爬取微信文章（含图片），起始URL: {start_url}")
        print(f"🎯 目标文章数量: {max_articles}")

        saved_files = []
        urls_to_crawl = [start_url]
        total_images = 0
        search_attempted = False

        while len(self.articles) < max_articles and urls_to_crawl:
            current_url = urls_to_crawl.pop(0)

            if current_url in self.crawled_urls:
                continue

            # 爬取当前文章
            article = self.crawl_single_article(current_url)

            if article:
                self.articles.append(article)
                total_images += article.get('image_count', 0)

                # 保存文章
                saved_file = self.save_article_to_markdown(article)
                if saved_file:
                    saved_files.append(saved_file)

                # 添加发现的新链接到待爬取列表
                if 'found_links' in article:
                    for link in article['found_links']:
                        if link not in self.crawled_urls and link not in urls_to_crawl:
                            urls_to_crawl.append(link)

                print(f"📊 进度: {len(self.articles)}/{max_articles} 篇文章，{total_images} 张图片")
                time.sleep(3)
            else:
                time.sleep(2)

            # 如果没有更多链接且还没有尝试搜索，则进行搜索
            if not urls_to_crawl and len(self.articles) < max_articles and not search_attempted:
                print("🔍 正在搜索更多相关文章...")
                search_attempted = True

                # 使用公众号信息进行搜索
                if self.account_info:
                    account_name = self.account_info.get('name', '')
                    biz = self.account_info.get('biz', '')

                    if account_name:
                        search_results = self.search_account_articles(
                            account_name,
                            biz,
                            max_results=max_articles - len(self.articles) + 10
                        )

                        for link in search_results:
                            if link not in self.crawled_urls and link not in urls_to_crawl:
                                urls_to_crawl.append(link)

                if not urls_to_crawl:
                    print("❌ 未找到更多相关文章，结束爬取")
                    break

        print(f"🎉 爬取完成！共爬取 {len(self.articles)} 篇文章，{total_images} 张图片")
        return saved_files

    def generate_summary(self, saved_files: List[str]) -> str:
        """生成爬取总结"""
        account_name = self.account_info.get('name', '未知公众号')
        total_images = sum(article.get('image_count', 0) for article in self.articles)

        summary_content = f"""# 微信文章爬取总结（含图片）

**公众号:** {account_name}
**爬取时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**总文章数:** {len(self.articles)}
**总图片数:** {total_images}
**保存目录:** {self.output_dir}
**图片目录:** {self.images_dir}

## 文章列表

"""

        for i, article in enumerate(self.articles, 1):
            image_count = article.get('image_count', 0)
            image_info = f" (含 {image_count} 张图片)" if image_count > 0 else ""
            summary_content += f"{i}. **{article['title']}**{image_info}\n"
            summary_content += f"   - 作者: {article['author']}\n"
            summary_content += f"   - 发布时间: {article['publish_time']}\n"
            summary_content += f"   - 原文链接: {article['url']}\n\n"

        summary_content += f"""
## 保存的文件

"""

        for i, filepath in enumerate(saved_files, 1):
            filename = os.path.basename(filepath)
            summary_content += f"{i}. {filename}\n"

        summary_content += f"""

## 图片统计

- **总图片数:** {total_images} 张
- **图片保存位置:** `{self.images_dir}`
- **图片组织方式:** 按文章标题分目录存放
- **支持格式:** JPG, PNG, GIF, WebP等

## 使用说明

1. 文章以Markdown格式保存，图片链接已替换为本地路径
2. 图片按文章分目录存放在 `images/` 文件夹下
3. 在Obsidian等Markdown编辑器中可以正常显示图片
4. 图片文件名包含URL哈希值，避免重复下载
"""

        # 保存总结文件
        summary_path = os.path.join(self.output_dir, f"爬取总结_含图片_{account_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")

        try:
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary_content)
            print(f"📋 总结文件已保存: {os.path.basename(summary_path)}")
            return summary_path
        except Exception as e:
            print(f"❌ 保存总结文件失败: {str(e)}")
            return None


def main():
    """主函数"""
    # 目标URL
    start_url = "https://mp.weixin.qq.com/s/qJN0eQliHRFl2WbIt4czSg"

    # 创建爬虫实例
    crawler = WeChatCrawlerWithImages(output_dir="E:/mcp-test/obsidian wang/微信好文")

    try:
        # 开始爬取（默认爬取46篇文章）
        saved_files = crawler.crawl_articles(start_url, max_articles=46)

        # 生成总结
        summary_file = crawler.generate_summary(saved_files)

        print("\n" + "="*60)
        print("🎉 爬取任务完成！")
        print(f"📊 共爬取文章: {len(crawler.articles)} 篇")
        total_images = sum(article.get('image_count', 0) for article in crawler.articles)
        print(f"🖼️  共下载图片: {total_images} 张")
        print(f"📁 保存目录: {crawler.output_dir}")
        print(f"🖼️  图片目录: {crawler.images_dir}")
        if summary_file:
            print(f"📋 总结文件: {summary_file}")
        print("="*60)

    except Exception as e:
        print(f"❌ 爬取过程中出现错误: {str(e)}")


if __name__ == "__main__":
    print("🚀 支持图片下载的微信公众号文章爬虫启动...")
    main()