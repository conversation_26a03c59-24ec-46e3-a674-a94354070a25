# PowerShell版本的视频字幕提取器
# 智能视频字幕提取器 (支持转录和翻译)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "智能视频字幕提取器 (支持转录和翻译)" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Python是否安装
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Python已安装: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python未找到"
    }
} catch {
    Write-Host "✗ 错误: 未找到Python，请先安装Python" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查FFmpeg是否安装
try {
    $ffmpegVersion = ffmpeg -version 2>$null | Select-Object -First 1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ FFmpeg已安装" -ForegroundColor Green
    } else {
        throw "FFmpeg未找到"
    }
} catch {
    Write-Host "⚠ 警告: 未找到FFmpeg，程序可能无法正常工作" -ForegroundColor Yellow
    Write-Host "请运行 install_dependencies.bat 安装依赖" -ForegroundColor Yellow
    Write-Host ""
}

# 检查vidoin目录
if (-not (Test-Path "vidoin")) {
    Write-Host "创建vidoin目录..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Name "vidoin" | Out-Null
    Write-Host "请将视频文件放入vidoin目录后重新运行此脚本" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 0
}

# 检查vidoin目录是否有视频文件
$videoFiles = Get-ChildItem -Path "vidoin" -Include "*.mp4", "*.avi", "*.mkv", "*.mov", "*.wmv", "*.flv", "*.m4v", "*.webm" -Recurse
if ($videoFiles.Count -eq 0) {
    Write-Host "vidoin目录中未找到视频文件" -ForegroundColor Yellow
    Write-Host "请将视频文件放入vidoin目录后重新运行此脚本" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 0
}

Write-Host "找到 $($videoFiles.Count) 个视频文件" -ForegroundColor Green
Write-Host ""

# 显示处理模式选择
Write-Host "请选择处理模式:" -ForegroundColor Cyan
Write-Host "1. 仅提取现有字幕" -ForegroundColor White
Write-Host "2. 启用转录功能（无字幕视频）" -ForegroundColor White
Write-Host "3. 智能模式（中文直接转录，英文转录后翻译）" -ForegroundColor Yellow
Write-Host "4. 强制翻译模式（所有内容都翻译）" -ForegroundColor White
Write-Host "5. 自定义参数" -ForegroundColor White
Write-Host ""

do {
    $choice = Read-Host "请输入选择 (1-5)"
} while ($choice -notmatch '^[1-5]$')

switch ($choice) {
    "1" {
        Write-Host "开始提取现有字幕..." -ForegroundColor Green
        python video_subtitle_extractor.py --dir vidoin --verbose --disable-auto-translate
    }
    "2" {
        Write-Host "开始提取字幕并转录无字幕视频..." -ForegroundColor Green
        python video_subtitle_extractor.py --dir vidoin --verbose --enable-transcribe --disable-auto-translate
    }
    "3" {
        $apiKey = Read-Host "请输入GLM-4-Flash API密钥"
        if ([string]::IsNullOrWhiteSpace($apiKey)) {
            Write-Host "API密钥不能为空，使用默认密钥" -ForegroundColor Yellow
            $apiKey = "7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0"
        }
        Write-Host "开始智能处理（中文直接转录，英文转录后翻译）..." -ForegroundColor Green
        python video_subtitle_extractor.py --dir vidoin --verbose --enable-transcribe --openai-api-key "$apiKey" --openai-base-url "https://open.bigmodel.cn/api/paas/v4"
    }
    "4" {
        $apiKey = Read-Host "请输入GLM-4-Flash API密钥"
        if ([string]::IsNullOrWhiteSpace($apiKey)) {
            Write-Host "API密钥不能为空，使用默认密钥" -ForegroundColor Yellow
            $apiKey = "7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0"
        }
        Write-Host "开始强制翻译模式（所有内容都翻译）..." -ForegroundColor Green
        python video_subtitle_extractor.py --dir vidoin --verbose --enable-transcribe --force-translate --openai-api-key "$apiKey" --openai-base-url "https://open.bigmodel.cn/api/paas/v4"
    }
    "5" {
        Write-Host "自定义参数模式" -ForegroundColor Cyan
        Write-Host "可用参数:" -ForegroundColor White
        Write-Host "--enable-transcribe : 启用转录" -ForegroundColor Gray
        Write-Host "--force-translate : 强制翻译所有内容" -ForegroundColor Gray
        Write-Host "--disable-auto-translate : 禁用智能翻译" -ForegroundColor Gray
        Write-Host "--openai-api-key `"key`" : GLM API密钥" -ForegroundColor Gray
        Write-Host "--transcribe-language `"zh`" : 转录语言" -ForegroundColor Gray
        Write-Host "--process-type clean : 清理字幕" -ForegroundColor Gray
        Write-Host "--keep-srt : 保留SRT文件" -ForegroundColor Gray
        Write-Host ""
        $customArgs = Read-Host "请输入自定义参数"
        if ([string]::IsNullOrWhiteSpace($customArgs)) {
            $customArgs = "--enable-transcribe"
        }
        python video_subtitle_extractor.py --dir vidoin --verbose $customArgs.Split(' ')
    }
    default {
        Write-Host "无效选择，使用智能模式（需要API密钥）" -ForegroundColor Yellow
        $apiKey = Read-Host "请输入GLM-4-Flash API密钥"
        if ([string]::IsNullOrWhiteSpace($apiKey)) {
            $apiKey = "7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0"
        }
        python video_subtitle_extractor.py --dir vidoin --verbose --enable-transcribe --openai-api-key "$apiKey" --openai-base-url "https://open.bigmodel.cn/api/paas/v4"
    }
}

Write-Host ""
Write-Host "处理完成！" -ForegroundColor Green
Write-Host "提取的字幕文件(.dm格式)已保存在vidoin目录中" -ForegroundColor Green
Write-Host ""
Read-Host "按任意键退出"
