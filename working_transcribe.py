#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作版本的转录脚本 - 生成字幕模板和DM文件
"""

import subprocess
import json
import re
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_tools():
    """检查必要工具"""
    tools = ['ffmpeg', 'ffprobe']
    for tool in tools:
        try:
            result = subprocess.run([tool, '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info(f"✓ {tool}可用")
            else:
                logger.error(f"✗ {tool}不可用")
                return False
        except Exception as e:
            logger.error(f"✗ {tool}检查失败: {e}")
            return False
    return True

def get_video_duration(video_path):
    """获取视频时长"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', str(video_path)
        ]
        
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=30,
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            data = json.loads(result.stdout)
            duration = float(data.get('format', {}).get('duration', 0))
            logger.info(f"视频时长: {duration:.2f}秒 ({duration/60:.1f}分钟)")
            return duration
        return 0
        
    except Exception as e:
        logger.error(f"获取视频时长失败: {e}")
        return 0

def create_subtitle_template(duration, output_path, segment_duration=30):
    """创建字幕模板"""
    try:
        logger.info(f"创建字幕模板，每段{segment_duration}秒")
        
        segments = []
        current_time = 0
        segment_num = 1
        
        while current_time < duration:
            end_time = min(current_time + segment_duration, duration)
            
            # 转换为SRT时间格式
            start_h = int(current_time // 3600)
            start_m = int((current_time % 3600) // 60)
            start_s = int(current_time % 60)
            start_ms = int((current_time % 1) * 1000)
            
            end_h = int(end_time // 3600)
            end_m = int((end_time % 3600) // 60)
            end_s = int(end_time % 60)
            end_ms = int((end_time % 1) * 1000)
            
            start_srt = f"{start_h:02d}:{start_m:02d}:{start_s:02d},{start_ms:03d}"
            end_srt = f"{end_h:02d}:{end_m:02d}:{end_s:02d},{end_ms:03d}"
            
            # 生成时间描述
            start_desc = f"{int(current_time//60)}:{int(current_time%60):02d}"
            end_desc = f"{int(end_time//60)}:{int(end_time%60):02d}"
            
            segment_text = f"""[{start_desc}-{end_desc}] 请在此添加字幕内容"""
            
            segments.append(f"""{segment_num}
{start_srt} --> {end_srt}
{segment_text}

""")
            
            current_time = end_time
            segment_num += 1
        
        srt_content = "".join(segments)
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(srt_content)
        
        logger.info(f"✓ 字幕模板已创建: {output_path}")
        logger.info(f"  共{segment_num-1}个片段，每段{segment_duration}秒")
        return True
        
    except Exception as e:
        logger.error(f"创建字幕模板失败: {e}")
        return False

def srt_to_dm(srt_path, dm_path):
    """将SRT转换为DM弹幕格式"""
    try:
        logger.info(f"转换SRT到DM格式...")
        
        with open(srt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析SRT格式
        pattern = r'(\d+)\n(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})\n(.*?)(?=\n\d+\n|\n*$)'
        matches = re.findall(pattern, content, re.DOTALL)
        
        dm_lines = []
        for match in matches:
            # 解析开始时间
            time_str = match[1].replace(',', '.')
            parts = time_str.split(':')
            start_seconds = int(parts[0]) * 3600 + int(parts[1]) * 60 + float(parts[2])
            
            # 获取文本内容
            text = match[3].strip().replace('\n', ' ')
            
            # 只处理非模板内容
            if text and not text.startswith('[') and '请在此添加' not in text:
                # DM格式: 时间,类型,字号,颜色,时间戳,弹幕池,用户ID,弹幕ID,内容
                dm_line = f"{start_seconds:.3f},1,25,16777215,{int(start_seconds*1000)},0,user,0,{text}"
                dm_lines.append(dm_line)
        
        # 写入DM文件
        with open(dm_path, 'w', encoding='utf-8') as f:
            if dm_lines:
                f.write('\n'.join(dm_lines))
            else:
                # 如果没有实际内容，创建一个示例
                f.write("0.000,1,25,16777215,0,0,user,0,请编辑SRT文件添加字幕内容后重新转换")
        
        logger.info(f"✓ DM文件已创建: {dm_path}")
        if dm_lines:
            logger.info(f"  包含{len(dm_lines)}条弹幕")
        else:
            logger.info("  包含示例内容，请编辑SRT文件后重新转换")
        
        return True
        
    except Exception as e:
        logger.error(f"SRT转DM失败: {e}")
        return False

def process_video(video_path):
    """处理单个视频文件"""
    logger.info(f"处理视频: {video_path.name}")
    
    # 获取视频时长
    duration = get_video_duration(video_path)
    if duration <= 0:
        logger.error("无法获取视频时长")
        return False
    
    # 生成输出文件路径
    base_name = video_path.stem
    srt_path = video_path.parent / f"{base_name}_template.srt"
    dm_path = video_path.parent / f"{base_name}_template.dm"
    
    # 创建字幕模板
    if not create_subtitle_template(duration, srt_path):
        return False
    
    # 转换为DM格式
    if not srt_to_dm(srt_path, dm_path):
        return False
    
    logger.info("✓ 视频处理完成")
    return True

def main():
    """主函数"""
    print("=== 视频字幕模板生成器 ===")
    
    # 检查工具
    if not check_tools():
        print("✗ 必要工具不可用，请安装FFmpeg")
        return
    
    # 查找视频文件
    video_dir = Path("vidoin")
    if not video_dir.exists():
        print("✗ vidoin目录不存在")
        return
    
    video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.m4v', '.webm'}
    video_files = []
    
    for ext in video_extensions:
        video_files.extend(video_dir.glob(f"*{ext}"))
    
    if not video_files:
        print("✗ 未找到视频文件")
        return
    
    logger.info(f"找到 {len(video_files)} 个视频文件")
    
    # 处理所有视频文件
    success_count = 0
    for video_file in video_files:
        if process_video(video_file):
            success_count += 1
    
    # 输出结果
    print(f"\n=== 处理完成 ===")
    print(f"总文件数: {len(video_files)}")
    print(f"成功: {success_count}")
    print(f"失败: {len(video_files) - success_count}")
    
    if success_count > 0:
        print(f"\n生成的文件:")
        print(f"- *_template.srt : 字幕模板文件（可编辑）")
        print(f"- *_template.dm  : 弹幕格式文件")
        print(f"\n使用说明:")
        print(f"1. 编辑SRT文件，将模板内容替换为实际字幕")
        print(f"2. 重新运行此脚本更新DM文件")
        print(f"3. 或者手动运行: python working_transcribe.py")

if __name__ == "__main__":
    main()
