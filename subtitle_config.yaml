# 视频字幕提取器配置文件

# 视频文件目录
video_directory: "vidoin"

# 支持的视频格式
supported_video_formats:
  - ".mp4"
  - ".avi"
  - ".mkv"
  - ".mov"
  - ".wmv"
  - ".flv"
  - ".m4v"
  - ".webm"
  - ".ts"
  - ".mts"

# 字幕提取设置
subtitle_settings:
  # 输出格式 (srt, ass, vtt)
  intermediate_format: "srt"
  
  # 是否保留中间文件
  keep_intermediate_files: false
  
  # 字幕编码
  encoding: "utf-8"
  
  # 超时设置（秒）
  extraction_timeout: 120
  probe_timeout: 30

# DM弹幕格式设置
dm_settings:
  # 默认字体大小
  font_size: 25
  
  # 默认颜色 (白色)
  color: 16777215
  
  # 弹幕类型 (1=滚动, 4=底部, 5=顶部)
  type: 1
  
  # 弹幕池
  pool: 0

# 日志设置
logging:
  # 日志级别 (DEBUG, INFO, WARNING, ERROR)
  level: "INFO"
  
  # 日志文件
  file: "subtitle_extractor.log"
  
  # 是否输出到控制台
  console: true

# FFmpeg设置
ffmpeg:
  # FFmpeg可执行文件路径 (留空自动检测)
  path: ""
  
  # 额外参数
  extra_args: []
