#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的视频转录脚本 - 不依赖Whisper
"""

import subprocess
import json
import tempfile
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_ffmpeg():
    """检查FFmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except:
        return False

def extract_audio(video_path, audio_path):
    """从视频中提取音频"""
    try:
        cmd = [
            'ffmpeg', '-i', str(video_path),
            '-vn', '-acodec', 'pcm_s16le', '-ar', '16000', '-ac', '1',
            '-y', str(audio_path)
        ]
        
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=300,
            encoding='utf-8',
            errors='ignore'
        )
        
        return result.returncode == 0
        
    except Exception as e:
        logger.error(f"音频提取失败: {e}")
        return False

def get_audio_duration(audio_path):
    """获取音频时长"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', str(audio_path)
        ]
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=30,
            encoding='utf-8',
            errors='ignore'
        )
        
        if result.returncode == 0:
            data = json.loads(result.stdout)
            return float(data.get('format', {}).get('duration', 0))
        return 0
        
    except Exception as e:
        logger.error(f"获取音频时长失败: {e}")
        return 0

def seconds_to_srt_time(seconds):
    """将秒数转换为SRT时间格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

def create_template_srt(duration, output_path):
    """创建字幕模板"""
    try:
        # 创建每30秒一个字幕段的模板
        segments = []
        current_time = 0
        segment_duration = 30  # 每段30秒
        segment_num = 1
        
        while current_time < duration:
            end_time = min(current_time + segment_duration, duration)
            start_srt = seconds_to_srt_time(current_time)
            end_srt = seconds_to_srt_time(end_time)
            
            segments.append(f"{segment_num}\n{start_srt} --> {end_srt}\n[请在此添加字幕内容 - 第{segment_num}段]\n")
            
            current_time = end_time
            segment_num += 1
        
        srt_content = "\n".join(segments)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(srt_content)
        
        return True
        
    except Exception as e:
        logger.error(f"创建字幕模板失败: {e}")
        return False

def srt_to_dm(srt_path, dm_path):
    """将SRT转换为DM格式"""
    try:
        with open(srt_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单的SRT解析
        import re
        pattern = r'(\d+)\n(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})\n(.*?)(?=\n\d+\n|\n*$)'
        matches = re.findall(pattern, content, re.DOTALL)
        
        dm_lines = []
        for match in matches:
            start_time_str = match[1].replace(',', '.')
            parts = start_time_str.split(':')
            start_seconds = int(parts[0]) * 3600 + int(parts[1]) * 60 + float(parts[2])
            
            text = match[3].strip().replace('\n', ' ')
            if text and not text.startswith('[请在此添加'):
                dm_line = f"{start_seconds:.3f},1,25,16777215,{int(start_seconds*1000)},0,user,0,{text}"
                dm_lines.append(dm_line)
        
        with open(dm_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(dm_lines))
        
        return True
        
    except Exception as e:
        logger.error(f"SRT转DM失败: {e}")
        return False

def process_video(video_path):
    """处理单个视频文件"""
    logger.info(f"处理视频: {video_path.name}")
    
    if not check_ffmpeg():
        logger.error("FFmpeg未安装，无法处理视频")
        return False
    
    # 创建临时音频文件
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_audio:
        temp_audio_path = Path(temp_audio.name)
    
    try:
        # 提取音频
        logger.info("提取音频...")
        if not extract_audio(video_path, temp_audio_path):
            logger.error("音频提取失败")
            return False
        
        # 获取音频时长
        duration = get_audio_duration(temp_audio_path)
        if duration <= 0:
            logger.error("无法获取音频时长")
            return False
        
        logger.info(f"音频时长: {duration:.2f}秒")
        
        # 生成输出文件路径
        base_name = video_path.stem
        srt_path = video_path.parent / f"{base_name}_template.srt"
        dm_path = video_path.parent / f"{base_name}_template.dm"
        
        # 创建字幕模板
        logger.info("创建字幕模板...")
        if create_template_srt(duration, srt_path):
            logger.info(f"字幕模板已创建: {srt_path}")
            
            # 转换为DM格式
            if srt_to_dm(srt_path, dm_path):
                logger.info(f"DM文件已创建: {dm_path}")
                return True
        
        return False
        
    finally:
        # 清理临时文件
        try:
            temp_audio_path.unlink()
        except:
            pass

def main():
    """主函数"""
    video_dir = Path("vidoin")
    
    if not video_dir.exists():
        logger.error("vidoin目录不存在")
        return
    
    # 查找视频文件
    video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.m4v', '.webm'}
    video_files = []
    
    for ext in video_extensions:
        video_files.extend(video_dir.glob(f"*{ext}"))
    
    if not video_files:
        logger.info("未找到视频文件")
        return
    
    logger.info(f"找到 {len(video_files)} 个视频文件")
    
    success_count = 0
    for video_file in video_files:
        if process_video(video_file):
            success_count += 1
    
    logger.info(f"处理完成: {success_count}/{len(video_files)} 成功")
    print(f"\n处理完成!")
    print(f"成功: {success_count}")
    print(f"失败: {len(video_files) - success_count}")
    print(f"生成的模板文件可以手动编辑添加字幕内容")

if __name__ == "__main__":
    main()
