#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版微信公众号文章爬取器
支持多种URL获取方式和反爬机制绕过
"""

import requests
import time
import random
import json
import re
from pathlib import Path
from urllib.parse import urlparse, parse_qs, unquote
from bs4 import BeautifulSoup
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedWeChatCrawler:
    def __init__(self, output_dir="E:/mcp-test/obsidian wang/微信好文"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 模拟真实浏览器的请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1'
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 延迟设置
        self.min_delay = 2
        self.max_delay = 5
        
    def extract_urls_from_text(self, text):
        """从文本中提取微信文章URL"""
        # 匹配微信文章URL的正则表达式
        patterns = [
            r'https://mp\.weixin\.qq\.com/s/[A-Za-z0-9_-]+',
            r'https://mp\.weixin\.qq\.com/s\?[^"\s<>]+',
            r'http://mp\.weixin\.qq\.com/s/[A-Za-z0-9_-]+',
            r'http://mp\.weixin\.qq\.com/s\?[^"\s<>]+',
        ]
        
        urls = set()
        for pattern in patterns:
            matches = re.findall(pattern, text)
            urls.update(matches)
        
        return list(urls)
    
    def clean_wechat_url(self, url):
        """清理微信URL，移除追踪参数"""
        try:
            # 解析URL
            parsed = urlparse(url)
            
            # 如果是短链接格式，直接返回
            if '/s/' in parsed.path and '?' not in url:
                return url
            
            # 如果有查询参数，提取关键参数
            if parsed.query:
                params = parse_qs(parsed.query)
                
                # 保留必要参数
                essential_params = {}
                for key in ['__biz', 'mid', 'idx', 'sn', 'chksm']:
                    if key in params:
                        essential_params[key] = params[key][0]
                
                if essential_params:
                    query_string = '&'.join([f"{k}={v}" for k, v in essential_params.items()])
                    return f"{parsed.scheme}://{parsed.netloc}{parsed.path}?{query_string}"
            
            return url
            
        except Exception as e:
            logger.error(f"清理URL失败: {e}")
            return url
    
    def get_article_content(self, url):
        """获取文章内容"""
        try:
            # 清理URL
            clean_url = self.clean_wechat_url(url)
            logger.info(f"正在爬取: {clean_url}")
            
            # 随机延迟
            delay = random.uniform(self.min_delay, self.max_delay)
            time.sleep(delay)
            
            # 发送请求
            response = self.session.get(clean_url, timeout=30)
            response.raise_for_status()
            
            # 检查是否被重定向到错误页面
            if '该内容已被发布者删除' in response.text or '此内容因违规无法查看' in response.text:
                logger.warning(f"文章已被删除或违规: {clean_url}")
                return None
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取文章信息
            article_info = self.extract_article_info(soup, clean_url)
            
            if article_info:
                logger.info(f"成功获取文章: {article_info['title']}")
                return article_info
            else:
                logger.warning(f"无法解析文章内容: {clean_url}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败 {url}: {e}")
            return None
        except Exception as e:
            logger.error(f"处理文章失败 {url}: {e}")
            return None
    
    def extract_article_info(self, soup, url):
        """从HTML中提取文章信息"""
        try:
            # 提取标题
            title_elem = soup.find('h1', class_='rich_media_title') or soup.find('h1')
            title = title_elem.get_text().strip() if title_elem else "未知标题"
            
            # 提取作者
            author_elem = soup.find('a', class_='rich_media_meta_link') or soup.find('span', class_='rich_media_meta_text')
            author = author_elem.get_text().strip() if author_elem else "未知作者"
            
            # 提取发布时间
            time_elem = soup.find('em', id='publish_time') or soup.find('span', class_='rich_media_meta_text')
            publish_time = time_elem.get_text().strip() if time_elem else datetime.now().strftime('%Y-%m-%d')
            
            # 提取正文内容
            content_elem = soup.find('div', class_='rich_media_content') or soup.find('div', id='js_content')
            
            if not content_elem:
                logger.warning("未找到文章正文内容")
                return None
            
            # 清理内容
            content = self.clean_content(content_elem)
            
            # 提取图片
            images = self.extract_images(content_elem)
            
            return {
                'title': title,
                'author': author,
                'publish_time': publish_time,
                'content': content,
                'images': images,
                'url': url
            }
            
        except Exception as e:
            logger.error(f"提取文章信息失败: {e}")
            return None
    
    def clean_content(self, content_elem):
        """清理文章内容"""
        # 移除脚本和样式
        for script in content_elem(["script", "style"]):
            script.decompose()
        
        # 移除广告和无关元素
        for elem in content_elem.find_all(['div'], class_=re.compile(r'(ad|advertisement|promotion)')):
            elem.decompose()
        
        # 转换为Markdown格式
        content = self.html_to_markdown(content_elem)
        
        return content
    
    def html_to_markdown(self, element):
        """将HTML转换为Markdown"""
        # 简单的HTML到Markdown转换
        text = element.get_text()
        
        # 基本清理
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line and line not in cleaned_lines[-3:]:  # 避免重复行
                cleaned_lines.append(line)
        
        return '\n\n'.join(cleaned_lines)
    
    def extract_images(self, content_elem):
        """提取文章中的图片"""
        images = []
        img_tags = content_elem.find_all('img')
        
        for img in img_tags:
            src = img.get('data-src') or img.get('src')
            if src:
                images.append(src)
        
        return images
    
    def save_article(self, article_info):
        """保存文章到文件"""
        try:
            # 清理文件名
            safe_title = re.sub(r'[<>:"/\\|?*]', '_', article_info['title'])
            safe_title = safe_title[:100]  # 限制文件名长度
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{safe_title}_{timestamp}.md"
            filepath = self.output_dir / filename
            
            # 生成Markdown内容
            markdown_content = f"""# {article_info['title']}

**作者**: {article_info['author']}
**发布时间**: {article_info['publish_time']}
**原文链接**: {article_info['url']}
**爬取时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

---

{article_info['content']}

---

## 图片列表

"""
            
            # 添加图片链接
            for i, img_url in enumerate(article_info['images'], 1):
                markdown_content += f"{i}. ![图片{i}]({img_url})\n"
            
            # 保存文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logger.info(f"文章已保存: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"保存文章失败: {e}")
            return None
    
    def crawl_from_clipboard(self):
        """从剪贴板获取URL并爬取"""
        try:
            import pyperclip
            clipboard_text = pyperclip.paste()
            
            urls = self.extract_urls_from_text(clipboard_text)
            
            if not urls:
                logger.info("剪贴板中未找到微信文章URL")
                return []
            
            logger.info(f"从剪贴板中找到 {len(urls)} 个URL")
            return self.crawl_urls(urls)
            
        except ImportError:
            logger.error("需要安装pyperclip: pip install pyperclip")
            return []
        except Exception as e:
            logger.error(f"从剪贴板爬取失败: {e}")
            return []
    
    def crawl_from_file(self, file_path):
        """从文件中读取URL并爬取"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            urls = self.extract_urls_from_text(content)
            
            if not urls:
                logger.info(f"文件 {file_path} 中未找到微信文章URL")
                return []
            
            logger.info(f"从文件中找到 {len(urls)} 个URL")
            return self.crawl_urls(urls)
            
        except Exception as e:
            logger.error(f"从文件爬取失败: {e}")
            return []
    
    def crawl_urls(self, urls):
        """批量爬取URL列表"""
        results = []
        
        for i, url in enumerate(urls, 1):
            logger.info(f"处理第 {i}/{len(urls)} 个URL")
            
            article_info = self.get_article_content(url)
            
            if article_info:
                filepath = self.save_article(article_info)
                if filepath:
                    results.append(filepath)
            
            # 避免请求过快
            if i < len(urls):
                delay = random.uniform(self.min_delay, self.max_delay)
                logger.info(f"等待 {delay:.1f} 秒...")
                time.sleep(delay)
        
        return results

def main():
    """主函数"""
    print("=== 增强版微信公众号文章爬取器 ===")
    print("1. 从剪贴板爬取")
    print("2. 从文件爬取")
    print("3. 手动输入URL")
    print()
    
    crawler = EnhancedWeChatCrawler()
    
    choice = input("请选择模式 (1-3): ").strip()
    
    if choice == "1":
        print("正在从剪贴板获取URL...")
        results = crawler.crawl_from_clipboard()
        
    elif choice == "2":
        file_path = input("请输入文件路径: ").strip()
        results = crawler.crawl_from_file(file_path)
        
    elif choice == "3":
        print("请输入微信文章URL（每行一个，输入空行结束）:")
        urls = []
        while True:
            url = input().strip()
            if not url:
                break
            urls.append(url)
        
        if urls:
            results = crawler.crawl_urls(urls)
        else:
            print("未输入任何URL")
            return
    
    else:
        print("无效选择")
        return
    
    # 输出结果
    print(f"\n=== 爬取完成 ===")
    print(f"成功爬取: {len(results)} 篇文章")
    
    if results:
        print("保存的文件:")
        for filepath in results:
            print(f"- {filepath}")

if __name__ == "__main__":
    main()
