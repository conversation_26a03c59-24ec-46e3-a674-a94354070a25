# 字幕模板使用说明

## 🎉 成功生成字幕模板！

您的视频已成功处理，生成了以下文件：

### 📁 生成的文件
- `视频名_template.srt` - 字幕模板文件（可编辑）
- `视频名_template.dm` - 弹幕格式文件

### 📝 使用步骤

#### 步骤1：编辑SRT模板文件
打开 `*_template.srt` 文件，您会看到类似这样的内容：

```srt
1
00:00:00,000 --> 00:00:30,000
[0:00-0:30] 请在此添加字幕内容

2
00:00:30,000 --> 00:01:00,000
[0:30-1:00] 请在此添加字幕内容
```

#### 步骤2：替换模板内容
将 `[时间] 请在此添加字幕内容` 替换为实际的字幕文字：

```srt
1
00:00:00,000 --> 00:00:30,000
大家好，欢迎来到今天的分享

2
00:00:30,000 --> 00:01:00,000
今天我们要讨论的主题是数字主义
```

#### 步骤3：重新生成DM文件
编辑完成后，重新运行脚本更新DM文件：

```powershell
python working_transcribe.py
```

### 🔧 高级功能

#### 自定义时间段长度
如果您想要更短或更长的时间段，可以修改脚本中的 `segment_duration` 参数：

```python
# 在 working_transcribe.py 中修改这一行
create_subtitle_template(duration, srt_path, segment_duration=15)  # 改为15秒一段
```

#### 手动调整时间轴
您可以手动调整SRT文件中的时间轴，使其更精确地匹配语音：

```srt
1
00:00:00,000 --> 00:00:05,500
大家好

2
00:00:05,500 --> 00:00:12,000
欢迎来到今天的分享
```

### 📊 当前视频信息
- **文件名**: `7_6第二部分：世界洞见-3.从数字主义角度你会遭遇什么.mp4`
- **时长**: 13.2分钟 (789秒)
- **分段数**: 27个片段
- **每段时长**: 30秒

### 🎯 DM弹幕格式说明
生成的 `.dm` 文件格式为：
```
时间,类型,字号,颜色,时间戳,弹幕池,用户ID,弹幕ID,内容
0.000,1,25,16777215,0,0,user,0,字幕内容
```

参数说明：
- **时间**: 弹幕出现的时间（秒）
- **类型**: 1=滚动弹幕, 4=底部弹幕, 5=顶部弹幕
- **字号**: 字体大小（默认25）
- **颜色**: 颜色值（16777215=白色）

### 🚀 快速操作

#### 一键处理所有视频
```powershell
# 处理vidoin目录下的所有视频
python working_transcribe.py
```

#### 检查生成的文件
```powershell
# 查看生成的文件
Get-ChildItem vidoin\*template*
```

#### 编辑字幕文件
```powershell
# 使用记事本编辑
notepad "vidoin\视频名_template.srt"

# 或使用VS Code编辑
code "vidoin\视频名_template.srt"
```

### 💡 提示和技巧

1. **时间精度**: SRT格式支持毫秒精度，可以精确到0.001秒
2. **多行字幕**: 可以在一个时间段内添加多行文字
3. **特殊字符**: 支持中文、英文、标点符号等
4. **格式保持**: 保持SRT文件的格式结构不变

### 🔄 工作流程建议

1. **生成模板** → `python working_transcribe.py`
2. **编辑字幕** → 打开SRT文件，替换模板内容
3. **更新DM** → 再次运行脚本更新DM文件
4. **验证结果** → 检查生成的DM文件内容

### ❓ 常见问题

**Q: 如何修改时间段长度？**
A: 修改脚本中的 `segment_duration` 参数

**Q: 可以手动调整时间轴吗？**
A: 可以，直接编辑SRT文件中的时间

**Q: DM文件格式有什么用？**
A: 可以导入到支持弹幕的播放器中显示

**Q: 如何批量处理多个视频？**
A: 将所有视频放入vidoin目录，运行一次脚本即可

### 🎊 下一步

现在您可以：
1. 编辑生成的SRT模板文件
2. 添加实际的字幕内容
3. 重新运行脚本生成最终的DM文件
4. 享受您的字幕文件！
