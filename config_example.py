#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频字幕提取器配置示例
复制此文件为 config.py 并修改相应设置
"""

# GLM-4-Flash API配置
GLM_CONFIG = {
    "api_key": "7810b54663644292bcfd221e9ecf7f0dd.aPV0rs8StKq1FmD0",
    "base_url": "https://open.bigmodel.cn/api/paas/v4",
    "model": "GLM-4-Flash",
    "temperature": 0.3,
    "max_tokens": 4000
}

# 转录配置
TRANSCRIBE_CONFIG = {
    "method": "whisper",  # whisper 或 glm
    "whisper_model": "base",  # tiny, base, small, medium, large
    "language": None,  # 自动检测，或指定如 "zh", "en"
    "enable_gpu": False,  # 是否使用GPU加速
}

# 字幕处理配置
SUBTITLE_CONFIG = {
    "supported_formats": [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".m4v", ".webm", ".ts", ".mts"],
    "output_format": "dm",  # dm 或 srt
    "keep_intermediate": False,  # 是否保留中间文件
    "encoding": "utf-8"
}

# DM弹幕格式配置
DM_CONFIG = {
    "font_size": 25,
    "color": 16777215,  # 白色
    "type": 1,  # 1=滚动, 4=底部, 5=顶部
    "pool": 0,
    "user_id": "user",
    "danmaku_id": 0
}

# 文件路径配置
PATH_CONFIG = {
    "video_dir": "vidoin",
    "output_dir": None,  # None表示输出到视频同目录
    "log_file": "subtitle_extractor.log",
    "temp_dir": None  # None表示使用系统临时目录
}

# 处理配置
PROCESS_CONFIG = {
    "batch_size": 1,  # 批处理大小
    "timeout": {
        "extraction": 120,  # 字幕提取超时（秒）
        "transcription": 600,  # 转录超时（秒）
        "translation": 60  # 翻译超时（秒）
    },
    "retry_count": 3,  # 重试次数
    "parallel": False  # 是否并行处理
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",  # DEBUG, INFO, WARNING, ERROR
    "format": "%(asctime)s - %(levelname)s - %(message)s",
    "file_encoding": "utf-8",
    "console_output": True
}

# 高级配置
ADVANCED_CONFIG = {
    "clean_temp_files": True,  # 自动清理临时文件
    "backup_original": False,  # 备份原始文件
    "verify_output": True,  # 验证输出文件
    "progress_callback": None  # 进度回调函数
}

# 使用示例
USAGE_EXAMPLES = {
    "basic": "python video_subtitle_extractor.py",
    "transcribe": "python video_subtitle_extractor.py --enable-transcribe",
    "translate": "python video_subtitle_extractor.py --translate --openai-api-key 'your_key'",
    "full": "python video_subtitle_extractor.py --enable-transcribe --translate --openai-api-key 'your_key' --keep-srt"
}

# 快捷命令配置
SHORTCUTS = {
    "extract_only": {
        "enable_transcribe": False,
        "translate": False,
        "keep_srt": False
    },
    "transcribe_only": {
        "enable_transcribe": True,
        "translate": False,
        "keep_srt": True
    },
    "translate_only": {
        "enable_transcribe": False,
        "translate": True,
        "keep_srt": False
    },
    "full_process": {
        "enable_transcribe": True,
        "translate": True,
        "process_type": "clean",
        "keep_srt": True
    }
}
