#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分批微信公众号文章爬虫命令行工具
支持跳过已爬取文章，实现分批爬取功能
"""

import argparse
import sys
import os
import json
from pathlib import Path
from ultra_safe_wechat_crawler import UltraSafeWeChatCrawler

class BatchWeChatCrawler(UltraSafeWeChatCrawler):
    def __init__(self, output_dir: str = "C:/Users/<USER>/Documents/Obsidian Vault/微信好文"):
        super().__init__(output_dir)
        self.crawled_urls_file = self.output_dir / "crawled_urls.json"
        self.load_crawled_urls_history()
    
    def load_crawled_urls_history(self):
        """加载已爬取URL历史记录"""
        try:
            if self.crawled_urls_file.exists():
                with open(self.crawled_urls_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.crawled_urls.update(data.get('crawled_urls', []))
                    print(f"📚 加载历史记录: {len(self.crawled_urls)} 个已爬取URL")
        except Exception as e:
            print(f"⚠️  加载历史记录失败: {e}")
    
    def save_crawled_urls_history(self):
        """保存已爬取URL历史记录"""
        try:
            data = {
                'crawled_urls': list(self.crawled_urls),
                'last_update': self.articles[-1]['crawl_time'] if self.articles else None,
                'total_articles': len(self.articles)
            }
            with open(self.crawled_urls_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 保存历史记录: {len(self.crawled_urls)} 个URL")
        except Exception as e:
            print(f"❌ 保存历史记录失败: {e}")
    
    def crawl_articles_batch(self, start_url: str, max_articles: int = 20, skip_count: int = 0) -> List[str]:
        """分批爬取文章"""
        print(f"🛡️  启动分批微信爬虫")
        print(f"📄 起始URL: {start_url}")
        print(f"🎯 目标文章数: {max_articles}")
        print(f"⏭️  跳过文章数: {skip_count}")
        print(f"📚 历史已爬取: {len(self.crawled_urls)} 个URL")
        print("-" * 80)
        
        saved_files = []
        
        # 第一阶段：爬取起始文章（如果未爬取过）
        if start_url not in self.crawled_urls:
            print("📍 第一阶段：爬取起始文章...")
            first_article = self.crawl_single_article_ultra_safe(start_url)
            
            if first_article:
                self.articles.append(first_article)
                self.crawled_urls.add(start_url)
                
                saved_file = self.save_article_safe(first_article)
                if saved_file:
                    saved_files.append(saved_file)
        else:
            print("📍 起始文章已爬取过，跳过...")
        
        # 第二阶段：搜索并分批爬取
        if self.account_info.get('name') and len(self.articles) < max_articles:
            account_name = self.account_info['name']
            biz = self.account_info.get('biz')
            
            print(f"\n🔍 第二阶段：搜索 '{account_name}' 的文章...")
            
            # 搜索更多URL
            search_urls = self.search_with_ultra_safe_strategy(
                account_name, biz, max_articles * 3  # 搜索更多URL以支持跳过
            )
            
            # 过滤已爬取的URL
            new_urls = [url for url in search_urls if url not in self.crawled_urls]
            print(f"🎯 发现新URL: {len(new_urls)} 个")
            
            # 应用跳过逻辑
            if skip_count > 0 and len(new_urls) > skip_count:
                urls_to_crawl = new_urls[skip_count:]
                print(f"⏭️  跳过前 {skip_count} 个URL，剩余 {len(urls_to_crawl)} 个")
            else:
                urls_to_crawl = new_urls
                print(f"📋 准备爬取 {len(urls_to_crawl)} 个URL")
            
            # 第三阶段：批量爬取
            print(f"\n📚 第三阶段：批量爬取文章...")
            
            crawled_count = 0
            for i, url in enumerate(urls_to_crawl):
                if crawled_count >= max_articles:
                    print(f"🎯 已达到目标文章数量: {max_articles}")
                    break
                
                print(f"\n📊 进度: {crawled_count}/{max_articles} | 处理第 {i+1}/{len(urls_to_crawl)} 个URL")
                
                article = self.crawl_single_article_ultra_safe(url)
                
                if article:
                    if self._is_target_account_article_safe(article):
                        self.articles.append(article)
                        self.crawled_urls.add(url)
                        
                        saved_file = self.save_article_safe(article)
                        if saved_file:
                            saved_files.append(saved_file)
                            crawled_count += 1
                    else:
                        print(f"⚠️  文章不属于目标账号，跳过")
                        self.crawled_urls.add(url)  # 标记为已处理
        
        # 保存历史记录
        self.save_crawled_urls_history()
        
        # 生成总结
        self.generate_summary_safe(saved_files)
        
        print(f"\n🎉 分批爬取完成！")
        print(f"📊 本批成功爬取: {len(saved_files)} 篇文章")
        print(f"📚 累计已爬取: {len(self.crawled_urls)} 个URL")
        print(f"📁 保存目录: {self.output_dir}")
        
        return saved_files

def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(
        description='分批微信公众号文章爬虫 - 支持跳过和分批爬取',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 第一批：爬取前10篇
  python batch_wechat_crawler_cli.py -u "URL" -n 10
  
  # 第二批：跳过前10篇，爬取第11-20篇
  python batch_wechat_crawler_cli.py -u "URL" -n 10 --skip 10
  
  # 第三批：跳过前20篇，爬取第21-30篇
  python batch_wechat_crawler_cli.py -u "URL" -n 10 --skip 20
  
  # 查看已爬取历史
  python batch_wechat_crawler_cli.py --show-history
  
  # 清除历史记录重新开始
  python batch_wechat_crawler_cli.py --clear-history

分批爬取策略:
  🔄 自动记录已爬取URL，避免重复
  ⏭️ 支持跳过指定数量的文章
  📚 维护爬取历史，支持断点续传
  🛡️ 保持超安全爬取策略
        """
    )

    parser.add_argument(
        '-u', '--url',
        help='微信公众号文章URL（起始文章）'
    )

    parser.add_argument(
        '-n', '--num-articles',
        type=int,
        default=10,
        help='本批要爬取的文章数量 (默认: 10)'
    )

    parser.add_argument(
        '--skip',
        type=int,
        default=0,
        help='跳过的文章数量 (默认: 0)'
    )

    parser.add_argument(
        '-o', '--output-dir',
        default="C:/Users/<USER>/Documents/Obsidian Vault/微信好文",
        help='输出目录 (默认: C:/Users/<USER>/Documents/Obsidian Vault/微信好文)'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='显示详细输出'
    )

    parser.add_argument(
        '--min-delay',
        type=float,
        default=10.0,
        help='最小延迟时间（秒）(默认: 10.0)'
    )

    parser.add_argument(
        '--max-delay',
        type=float,
        default=20.0,
        help='最大延迟时间（秒）(默认: 20.0)'
    )

    parser.add_argument(
        '--show-history',
        action='store_true',
        help='显示已爬取历史记录'
    )

    parser.add_argument(
        '--clear-history',
        action='store_true',
        help='清除历史记录'
    )

    args = parser.parse_args()

    # 创建爬虫实例
    crawler = BatchWeChatCrawler(output_dir=args.output_dir)

    # 显示历史记录
    if args.show_history:
        print("📚 已爬取历史记录:")
        if crawler.crawled_urls_file.exists():
            try:
                with open(crawler.crawled_urls_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(f"   📊 总URL数: {len(data.get('crawled_urls', []))}")
                    print(f"   📄 总文章数: {data.get('total_articles', 0)}")
                    print(f"   🕒 最后更新: {data.get('last_update', '未知')}")
                    print(f"   📁 历史文件: {crawler.crawled_urls_file}")
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        else:
            print("   📝 暂无历史记录")
        return

    # 清除历史记录
    if args.clear_history:
        try:
            if crawler.crawled_urls_file.exists():
                crawler.crawled_urls_file.unlink()
                print("✅ 历史记录已清除")
            else:
                print("📝 暂无历史记录需要清除")
        except Exception as e:
            print(f"❌ 清除历史记录失败: {e}")
        return

    # 验证必需参数
    if not args.url:
        print("❌ 错误: 必须提供起始文章URL")
        print("💡 使用 --show-history 查看历史记录")
        print("💡 使用 --clear-history 清除历史记录")
        sys.exit(1)

    # 验证URL
    if not args.url.startswith('http'):
        print("❌ 错误: URL必须以http或https开头")
        sys.exit(1)

    if 'mp.weixin.qq.com' not in args.url:
        print("❌ 错误: 请提供有效的微信公众号文章URL")
        sys.exit(1)

    # 显示配置信息
    print("🔄 分批微信公众号文章爬虫启动...")
    print(f"📄 起始文章URL: {args.url}")
    print(f"🎯 本批目标数量: {args.num_articles}")
    print(f"⏭️  跳过文章数: {args.skip}")
    print(f"📁 输出目录: {args.output_dir}")
    print(f"⏱️  延迟范围: {args.min_delay}-{args.max_delay}秒")
    print("-" * 80)

    try:
        # 设置延迟参数
        crawler.min_delay = args.min_delay
        crawler.max_delay = args.max_delay

        # 开始分批爬取
        saved_files = crawler.crawl_articles_batch(args.url, args.num_articles, args.skip)

        # 显示结果
        print("\n" + "="*80)
        if saved_files:
            print("🎉 分批爬取任务完成！")
            print(f"📊 本批成功爬取: {len(saved_files)} 篇文章")
            print(f"📚 累计已爬取: {len(crawler.crawled_urls)} 个URL")
            print(f"📁 保存目录: {args.output_dir}")
            
            if args.verbose and saved_files:
                print("\n📚 本批爬取的文章:")
                for i, filepath in enumerate(saved_files, 1):
                    filename = os.path.basename(filepath)
                    title = filename.replace('.md', '').rsplit('_', 2)[0]
                    print(f"  {i:2d}. {title}")
            
            print(f"\n💡 继续爬取下一批:")
            next_skip = args.skip + len(saved_files)
            print(f"   python batch_wechat_crawler_cli.py -u \"{args.url}\" -n {args.num_articles} --skip {next_skip}")
            
        else:
            print("❌ 本批爬取失败或无新文章")
            print("\n💡 可能的原因:")
            print("   - 所有文章都已爬取过")
            print("   - 跳过数量超过了可用文章数")
            print("   - 遇到反爬机制")
        
        print("="*80)

    except KeyboardInterrupt:
        print("\n⚠️  用户中断了爬取过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 爬取过程中出现错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
