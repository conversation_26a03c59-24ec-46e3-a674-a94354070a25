#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能分批微信公众号文章爬虫
优化速度控制，提高下载成功率，减少触发微信保护机制
"""

import requests
import re
import os
import time
import json
import random
from datetime import datetime, timedelta
from typing import List, Dict, Set
from urllib.parse import quote, unquote, urlparse
from bs4 import BeautifulSoup
import hashlib
from pathlib import Path

class SmartBatchWeChatCrawler:
    def __init__(self, output_dir: str = "C:/Users/<USER>/Documents/Obsidian Vault/微信好文"):
        """
        智能分批微信爬虫 - 优化速度控制和成功率

        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.crawled_urls: Set[str] = set()
        self.articles: List[Dict] = []
        self.account_info = {}

        # 智能延迟策略
        self.base_delay = 15  # 基础延迟15秒
        self.max_delay = 30   # 最大延迟30秒
        self.search_delay = 45  # 搜索间隔45秒
        self.batch_rest_time = 180  # 每批次间隔3分钟

        # 成功率监控
        self.success_count = 0
        self.failure_count = 0
        self.protection_count = 0
        self.last_success_time = None

        # 动态延迟调整
        self.current_delay_multiplier = 1.0
        self.max_delay_multiplier = 3.0

        # 高质量User-Agent池
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/119.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36'
        ]

        # 创建会话
        self.session = requests.Session()
        self._setup_smart_session()

        # 请求统计
        self.request_count = 0
        self.last_request_time = 0

        # 历史记录文件
        self.crawled_urls_file = self.output_dir / "crawled_urls.json"
        self.stats_file = self.output_dir / "crawl_stats.json"
        self.load_history()

    def _setup_smart_session(self):
        """设置智能会话参数"""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'DNT': '1',
        })

        # 设置超时
        self.session.timeout = 45

    def load_history(self):
        """加载历史记录"""
        try:
            # 加载已爬取URL
            if self.crawled_urls_file.exists():
                with open(self.crawled_urls_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.crawled_urls.update(data.get('crawled_urls', []))
                    self.account_info = data.get('account_info', {})
                    print(f"📚 加载历史URL: {len(self.crawled_urls)} 个")

            # 加载统计信息
            if self.stats_file.exists():
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    stats = json.load(f)
                    self.success_count = stats.get('success_count', 0)
                    self.failure_count = stats.get('failure_count', 0)
                    self.protection_count = stats.get('protection_count', 0)
                    self.current_delay_multiplier = stats.get('delay_multiplier', 1.0)
                    print(f"📊 历史统计 - 成功:{self.success_count} 失败:{self.failure_count} 保护:{self.protection_count}")

        except Exception as e:
            print(f"⚠️  加载历史记录失败: {e}")

    def save_history(self):
        """保存历史记录"""
        try:
            # 保存URL历史
            url_data = {
                'crawled_urls': list(self.crawled_urls),
                'account_info': self.account_info,
                'last_update': datetime.now().isoformat(),
                'total_articles': len(self.articles)
            }
            with open(self.crawled_urls_file, 'w', encoding='utf-8') as f:
                json.dump(url_data, f, ensure_ascii=False, indent=2)

            # 保存统计信息
            stats_data = {
                'success_count': self.success_count,
                'failure_count': self.failure_count,
                'protection_count': self.protection_count,
                'delay_multiplier': self.current_delay_multiplier,
                'last_update': datetime.now().isoformat(),
                'success_rate': self.get_success_rate()
            }
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"❌ 保存历史记录失败: {e}")

    def get_success_rate(self) -> float:
        """计算成功率"""
        total = self.success_count + self.failure_count + self.protection_count
        if total == 0:
            return 0.0
        return (self.success_count / total) * 100

    def _get_smart_headers(self) -> dict:
        """获取智能请求头"""
        headers = {
            'User-Agent': random.choice(self.user_agents),
            'Referer': random.choice([
                'https://mp.weixin.qq.com/',
                'https://weixin.qq.com/',
                'https://www.google.com/',
                'https://www.baidu.com/',
            ]),
        }

        # 随机添加一些可选头
        if random.random() > 0.6:
            headers['X-Requested-With'] = 'XMLHttpRequest'

        if random.random() > 0.7:
            headers['Sec-Ch-Ua'] = '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"'
            headers['Sec-Ch-Ua-Mobile'] = '?0'
            headers['Sec-Ch-Ua-Platform'] = '"Windows"'

        return headers

    def _smart_delay(self, operation_type: str = "normal"):
        """智能延迟策略"""
        # 基础延迟计算
        if operation_type == "search":
            base_delay = self.search_delay
        elif operation_type == "batch_rest":
            base_delay = self.batch_rest_time
        else:
            base_delay = self.base_delay

        # 应用动态延迟倍数
        adjusted_delay = base_delay * self.current_delay_multiplier

        # 添加随机波动
        random_factor = random.uniform(0.8, 1.4)
        final_delay = adjusted_delay * random_factor

        # 限制最大延迟
        max_allowed = base_delay * self.max_delay_multiplier
        final_delay = min(final_delay, max_allowed)

        # 检查请求频率
        current_time = time.time()
        if self.last_request_time > 0:
            time_since_last = current_time - self.last_request_time
            if time_since_last < self.base_delay:
                additional_delay = self.base_delay - time_since_last + random.uniform(2, 5)
                final_delay += additional_delay

        print(f"⏱️  智能延迟 {final_delay:.1f}秒 ({operation_type}, 倍数:{self.current_delay_multiplier:.1f})")
        time.sleep(final_delay)

        self.last_request_time = time.time()
        self.request_count += 1

        # 每15个请求后额外休息
        if self.request_count % 15 == 0:
            extra_delay = random.uniform(120, 240)  # 2-4分钟
            print(f"🛡️  防护性长休息 {extra_delay:.0f}秒...")
            time.sleep(extra_delay)

    def _adjust_delay_based_on_success(self, success: bool, is_protection: bool = False):
        """根据成功率动态调整延迟"""
        if success:
            self.success_count += 1
            self.last_success_time = datetime.now()
            # 成功时略微减少延迟倍数
            self.current_delay_multiplier = max(1.0, self.current_delay_multiplier * 0.95)
        elif is_protection:
            self.protection_count += 1
            # 遇到保护时大幅增加延迟
            self.current_delay_multiplier = min(self.max_delay_multiplier, self.current_delay_multiplier * 1.5)
            print(f"🛡️  检测到保护机制，延迟倍数调整为: {self.current_delay_multiplier:.1f}")
        else:
            self.failure_count += 1
            # 失败时适度增加延迟
            self.current_delay_multiplier = min(self.max_delay_multiplier, self.current_delay_multiplier * 1.2)

        # 显示当前成功率
        success_rate = self.get_success_rate()
        print(f"📊 当前成功率: {success_rate:.1f}% (成功:{self.success_count} 失败:{self.failure_count} 保护:{self.protection_count})")

    def _safe_request(self, url: str, operation_type: str = "normal") -> requests.Response:
        """智能安全请求方法"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                # 智能延迟
                self._smart_delay(operation_type)

                # 获取随机请求头
                headers = self._get_smart_headers()

                print(f"🌐 智能请求: {url[:60]}...")

                response = self.session.get(url, headers=headers, timeout=45)

                # 检查响应状态
                if response.status_code == 200:
                    # 检查是否被重定向到保护页面
                    if self._is_protection_page(response.text):
                        print(f"⚠️  检测到保护页面，调整策略...")
                        self._adjust_delay_based_on_success(False, is_protection=True)

                        # 遇到保护时等待更长时间
                        protection_delay = random.uniform(300, 600)  # 5-10分钟
                        print(f"🛡️  保护机制触发，等待 {protection_delay:.0f}秒...")
                        time.sleep(protection_delay)
                        continue

                    self._adjust_delay_based_on_success(True)
                    return response
                elif response.status_code == 429:
                    print(f"⚠️  请求过于频繁，大幅增加延迟...")
                    self._adjust_delay_based_on_success(False, is_protection=True)
                    time.sleep(random.uniform(600, 1200))  # 等待10-20分钟
                    continue
                else:
                    print(f"⚠️  HTTP {response.status_code}, 重试...")
                    self._adjust_delay_based_on_success(False)
                    time.sleep(random.uniform(60, 120))
                    continue

            except Exception as e:
                print(f"❌ 请求失败 (尝试 {attempt+1}/{max_retries}): {e}")
                self._adjust_delay_based_on_success(False)
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(120, 240))
                continue

        raise Exception(f"请求失败，已重试 {max_retries} 次")

    def _is_protection_page(self, html_content: str) -> bool:
        """检测是否为保护页面"""
        protection_keywords = [
            '名誉保护投诉指引',
            '该内容已被发布者删除',
            '此内容因违规无法查看',
            '链接已过期',
            '访问过于频繁',
            '请稍后再试',
            '系统检测到异常',
            '验证码',
            'captcha',
            '人机验证',
            '请输入验证码',
            '安全验证',
            '异常访问',
        ]

        for keyword in protection_keywords:
            if keyword in html_content:
                return True

        return False

    def extract_account_info_smart(self, html_content: str) -> Dict:
        """智能提取账号信息"""
        account_info = {}

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 提取公众号名称 - 多种方法
            name_selectors = [
                '#js_name',
                '.rich_media_meta_text',
                '.profile_nickname',
                '.account_nickname',
                'meta[property="og:title"]',
                'title',
                '.rich_media_meta_nickname'
            ]

            for selector in name_selectors:
                if selector.startswith('meta'):
                    elem = soup.select_one(selector)
                    if elem:
                        content = elem.get('content', '')
                        if content and len(content) > 1 and '微信' not in content and '公众号' not in content:
                            account_info['name'] = content.strip()
                            break
                else:
                    elem = soup.select_one(selector)
                    if elem:
                        name = elem.get_text().strip()
                        if name and len(name) > 1 and '微信' not in name and '公众号' not in name:
                            account_info['name'] = name
                            break

            # 提取biz参数 - 多种模式
            biz_patterns = [
                r'__biz=([^&"\']+)',
                r'"biz":"([^"]+)"',
                r'biz=([^&\s]+)',
                r'var\s+biz\s*=\s*["\']([^"\']+)["\']',
            ]

            for pattern in biz_patterns:
                match = re.search(pattern, html_content)
                if match:
                    biz = unquote(match.group(1))
                    if len(biz) > 10:  # biz通常比较长
                        account_info['biz'] = biz
                        break

            # 提取更多元信息
            if soup.select_one('meta[property="og:description"]'):
                desc = soup.select_one('meta[property="og:description"]').get('content', '')
                if desc:
                    account_info['description'] = desc[:200]

            print(f"📱 智能提取账号信息: {account_info}")

        except Exception as e:
            print(f"❌ 提取账号信息失败: {e}")

        return account_info

    def search_with_smart_strategy(self, account_name: str, biz: str = None, max_results: int = 30) -> List[str]:
        """智能搜索策略"""
        print(f"🔍 开始智能搜索: {account_name}")
        all_urls = set()

        # 智能搜索策略：根据成功率选择搜索引擎
        search_methods = []

        # 根据当前成功率决定搜索策略
        success_rate = self.get_success_rate()
        if success_rate > 70 or self.success_count < 5:
            # 成功率高或样本少时，使用更多搜索引擎
            search_methods = [
                self._search_baidu_smart,
                self._search_sogou_smart,
            ]
        else:
            # 成功率低时，只使用最稳定的搜索
            search_methods = [self._search_baidu_smart]

        for method in search_methods:
            try:
                print(f"🔍 使用 {method.__name__} 搜索...")
                urls = method(account_name, biz, max_results // len(search_methods))
                all_urls.update(urls)

                print(f"✅ {method.__name__} 发现 {len(urls)} 个URL")

                # 搜索间隔
                self._smart_delay("search")

                if len(all_urls) >= max_results:
                    break

            except Exception as e:
                print(f"❌ {method.__name__} 搜索失败: {e}")
                self._adjust_delay_based_on_success(False)
                continue

        print(f"🎯 智能搜索总共发现 {len(all_urls)} 个URL")
        return list(all_urls)[:max_results]

    def _search_baidu_smart(self, account_name: str, biz: str = None, max_results: int = 20) -> List[str]:
        """智能百度搜索"""
        urls = set()

        # 智能查询策略
        queries = [f'site:mp.weixin.qq.com "{account_name}"']

        if biz:
            queries.append(f'site:mp.weixin.qq.com __biz={biz[:15]}')  # 只用biz的前15个字符

        for query in queries[:1]:  # 保守策略，只用第一个查询
            try:
                search_url = f"https://www.baidu.com/s?wd={quote(query)}&pn=0&rn=30"

                response = self._safe_request(search_url, "search")
                soup = BeautifulSoup(response.text, 'html.parser')

                # 智能提取搜索结果
                for link in soup.find_all('a', href=True):
                    href = link.get('href', '')
                    if 'mp.weixin.qq.com/s' in href and 'baidu.com' not in href:
                        clean_url = self._extract_clean_url(href)
                        if clean_url and self._validate_wechat_url(clean_url):
                            urls.add(clean_url)
                            if len(urls) >= max_results:
                                break

                if len(urls) >= max_results:
                    break

            except Exception as e:
                print(f"❌ 百度搜索查询失败: {e}")
                continue

        return list(urls)

    def _search_sogou_smart(self, account_name: str, biz: str = None, max_results: int = 15) -> List[str]:
        """智能搜狗搜索"""
        urls = set()

        try:
            # 搜狗微信搜索
            search_url = f"https://weixin.sogou.com/weixin?type=1&query={quote(account_name)}"

            response = self._safe_request(search_url, "search")
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找文章链接
            for link in soup.find_all('a', href=True):
                href = link.get('href', '')
                if 'mp.weixin.qq.com' in href:
                    clean_url = self._extract_clean_url(href)
                    if clean_url and self._validate_wechat_url(clean_url):
                        urls.add(clean_url)
                        if len(urls) >= max_results:
                            break

        except Exception as e:
            print(f"❌ 搜狗搜索失败: {e}")

        return list(urls)

    def _extract_clean_url(self, url: str) -> str:
        """提取干净的URL"""
        if not url:
            return ""

        # 处理百度重定向
        if 'baidu.com' in url and 'url=' in url:
            try:
                from urllib.parse import parse_qs, urlparse
                parsed = urlparse(url)
                params = parse_qs(parsed.query)
                if 'url' in params:
                    return params['url'][0]
            except:
                pass

        # 直接返回微信URL
        if url.startswith('http') and 'mp.weixin.qq.com' in url:
            return url

        return ""

    def _validate_wechat_url(self, url: str) -> bool:
        """验证微信URL"""
        if not url or not url.startswith(('http://', 'https://')):
            return False

        if 'mp.weixin.qq.com/s' not in url:
            return False

        # 避免明显的错误URL
        invalid_patterns = [
            'baidu.com', 'sogou.com', 'google.com', 'bing.com',
            'redirect', 'link?url', 'jump'
        ]

        for pattern in invalid_patterns:
            if pattern in url:
                return False

        return True

    def crawl_single_article_smart(self, url: str) -> Dict:
        """智能单篇文章爬取"""
        try:
            print(f"📄 智能爬取: {url}")

            response = self._safe_request(url, "article")

            # 检查是否为保护页面
            if self._is_protection_page(response.text):
                print(f"⚠️  检测到保护页面，跳过此URL")
                return None

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取账号信息（如果是第一篇）
            if not self.account_info:
                self.account_info = self.extract_account_info_smart(response.text)

            # 提取文章信息
            article_info = self._extract_article_info_smart(soup, url)

            if article_info:
                print(f"✅ 成功爬取: {article_info['title']}")
                return article_info

            return None

        except Exception as e:
            print(f"❌ 爬取文章失败 {url}: {e}")
            return None

    def _extract_article_info_smart(self, soup, url: str) -> Dict:
        """智能提取文章信息"""
        try:
            # 智能提取标题
            title_selectors = [
                'h1.rich_media_title',
                'h1#activity-name',
                'h1',
                '.rich_media_title',
                'meta[property="og:title"]',
                'title',
                '.rich_media_area_primary .rich_media_title'
            ]

            title = "未知标题"
            for selector in title_selectors:
                if selector.startswith('meta'):
                    elem = soup.select_one(selector)
                    if elem:
                        title = elem.get('content', '').strip()
                        if title and len(title) > 3 and '微信' not in title:
                            break
                else:
                    elem = soup.select_one(selector)
                    if elem:
                        title = elem.get_text().strip()
                        if title and len(title) > 3:
                            break

            # 智能提取作者
            author = self.account_info.get('name', '未知作者')
            author_selectors = [
                '.rich_media_meta_text',
                '.profile_nickname',
                '#js_name',
                '.rich_media_meta_nickname',
                'meta[name="author"]'
            ]

            for selector in author_selectors:
                if selector.startswith('meta'):
                    elem = soup.select_one(selector)
                    if elem:
                        author_text = elem.get('content', '').strip()
                        if author_text and '微信' not in author_text:
                            author = author_text
                            break
                else:
                    elem = soup.select_one(selector)
                    if elem:
                        author_text = elem.get_text().strip()
                        if author_text and '微信' not in author_text:
                            author = author_text
                            break

            # 智能提取发布时间
            publish_time = self._extract_publish_time_smart(soup)

            # 智能提取内容
            content = self._extract_content_smart(soup)

            # 检查是否为有效文章
            if self._is_valid_article_smart(title, content):
                return {
                    'title': title,
                    'author': author,
                    'publish_time': publish_time,
                    'content': content,
                    'url': url,
                    'crawl_time': datetime.now().isoformat(),
                    'content_length': len(content)
                }

            return None

        except Exception as e:
            print(f"❌ 提取文章信息失败: {e}")
            return None

    def _extract_publish_time_smart(self, soup) -> str:
        """智能提取发布时间"""
        time_selectors = [
            '#publish_time',
            '.rich_media_meta_text',
            '[id*="time"]',
            'meta[property="article:published_time"]',
            '.rich_media_meta .rich_media_meta_text',
            'em[id*="time"]'
        ]

        for selector in time_selectors:
            if selector.startswith('meta'):
                elem = soup.select_one(selector)
                if elem:
                    time_text = elem.get('content', '').strip()
                    if re.search(r'\d{4}[-年]\d{1,2}[-月]\d{1,2}', time_text):
                        return time_text
            else:
                elem = soup.select_one(selector)
                if elem:
                    time_text = elem.get_text().strip()
                    if re.search(r'\d{4}[-年]\d{1,2}[-月]\d{1,2}', time_text):
                        return time_text

        return datetime.now().strftime('%Y-%m-%d')

    def _extract_content_smart(self, soup) -> str:
        """智能提取文章内容"""
        content_selectors = [
            '#js_content',
            '.rich_media_content',
            '.content',
            'article',
            '.post-content',
            '.rich_media_area_primary .rich_media_content'
        ]

        for selector in content_selectors:
            elem = soup.select_one(selector)
            if elem:
                # 移除脚本和样式
                for script in elem(["script", "style", "noscript"]):
                    script.decompose()

                # 移除广告和无关内容
                for ad in elem.select('.rich_media_tool, .qr_code_pc, .reward_qrcode'):
                    ad.decompose()

                text = elem.get_text()
                lines = (line.strip() for line in text.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                content = '\n'.join(chunk for chunk in chunks if chunk)

                if len(content) > 100:  # 确保内容足够长
                    return content

        return "无法提取内容"

    def _is_valid_article_smart(self, title: str, content: str) -> bool:
        """智能检查是否为有效文章"""
        # 检查标题
        invalid_title_keywords = [
            '名誉保护', '投诉指引', '系统提示', '访问异常',
            '验证码', '人机验证', '页面不存在', '链接已过期',
            '安全验证', '异常访问', '请稍后再试'
        ]

        for keyword in invalid_title_keywords:
            if keyword in title:
                return False

        # 检查内容长度和质量
        if len(content) < 100:
            return False

        # 检查内容关键词
        invalid_content_keywords = [
            '名誉保护投诉指引', '该内容已被发布者删除',
            '此内容因违规无法查看', '访问过于频繁',
            '请稍后再试', '系统检测到异常'
        ]

        for keyword in invalid_content_keywords:
            if keyword in content:
                return False

        # 检查内容质量（避免过短或重复内容）
        unique_chars = len(set(content.replace(' ', '').replace('\n', '')))
        if unique_chars < 50:  # 独特字符太少
            return False

        return True

    def save_article_smart(self, article: Dict) -> str:
        """智能保存文章"""
        try:
            safe_title = re.sub(r'[<>:"/\\|?*]', '_', article['title'])
            safe_title = safe_title[:100]

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{safe_title}_{timestamp}.md"
            filepath = self.output_dir / filename

            counter = 1
            while filepath.exists():
                filename = f"{safe_title}_{timestamp}_{counter}.md"
                filepath = self.output_dir / filename
                counter += 1

            markdown_content = f"""# {article['title']}

**作者:** {article['author']}
**发布时间:** {article['publish_time']}
**原文链接:** {article['url']}
**爬取时间:** {article['crawl_time']}
**内容长度:** {article.get('content_length', 0)} 字符

---

{article['content']}

---

*本文由智能分批微信爬虫抓取*
"""

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            print(f"💾 文章已保存: {filename}")
            return str(filepath)

        except Exception as e:
            print(f"❌ 保存文章失败: {e}")
            return None

    def crawl_articles_smart_batch(self, start_url: str, max_articles: int = 15, skip_count: int = 0) -> List[str]:
        """智能分批爬取文章"""
        print(f"🧠 启动智能分批微信爬虫")
        print(f"📄 起始URL: {start_url}")
        print(f"🎯 本批目标数: {max_articles}")
        print(f"⏭️  跳过文章数: {skip_count}")
        print(f"📊 当前成功率: {self.get_success_rate():.1f}%")
        print(f"🔄 延迟倍数: {self.current_delay_multiplier:.1f}")
        print("-" * 80)

        saved_files = []

        # 第一阶段：智能爬取起始文章
        if start_url not in self.crawled_urls:
            print("📍 第一阶段：智能爬取起始文章...")
            first_article = self.crawl_single_article_smart(start_url)

            if first_article:
                self.articles.append(first_article)
                self.crawled_urls.add(start_url)

                saved_file = self.save_article_smart(first_article)
                if saved_file:
                    saved_files.append(saved_file)
            else:
                print("❌ 起始文章爬取失败，可能遇到保护机制")
                return []
        else:
            print("📍 起始文章已爬取过，跳过...")

        # 第二阶段：智能搜索和分批爬取
        if self.account_info.get('name') and len(saved_files) < max_articles:
            account_name = self.account_info['name']
            biz = self.account_info.get('biz')

            print(f"\n🔍 第二阶段：智能搜索 '{account_name}' 的文章...")

            # 根据成功率调整搜索数量
            search_multiplier = 3 if self.get_success_rate() > 50 else 5
            search_urls = self.search_with_smart_strategy(
                account_name, biz, max_articles * search_multiplier
            )

            # 过滤已爬取的URL
            new_urls = [url for url in search_urls if url not in self.crawled_urls]
            print(f"🎯 发现新URL: {len(new_urls)} 个")

            # 应用跳过逻辑
            if skip_count > 0 and len(new_urls) > skip_count:
                urls_to_crawl = new_urls[skip_count:]
                print(f"⏭️  跳过前 {skip_count} 个URL，剩余 {len(urls_to_crawl)} 个")
            else:
                urls_to_crawl = new_urls
                print(f"📋 准备爬取 {len(urls_to_crawl)} 个URL")

            # 第三阶段：智能批量爬取
            print(f"\n📚 第三阶段：智能批量爬取...")

            crawled_count = 0
            consecutive_failures = 0

            for i, url in enumerate(urls_to_crawl):
                if crawled_count >= max_articles:
                    print(f"🎯 已达到本批目标文章数量: {max_articles}")
                    break

                # 连续失败检测
                if consecutive_failures >= 5:
                    print(f"⚠️  连续失败过多，进入保护模式...")
                    self._smart_delay("batch_rest")
                    consecutive_failures = 0

                print(f"\n📊 进度: {crawled_count}/{max_articles} | 处理第 {i+1}/{len(urls_to_crawl)} 个URL")

                article = self.crawl_single_article_smart(url)

                if article:
                    if self._is_target_account_article_smart(article):
                        self.articles.append(article)
                        self.crawled_urls.add(url)

                        saved_file = self.save_article_smart(article)
                        if saved_file:
                            saved_files.append(saved_file)
                            crawled_count += 1
                            consecutive_failures = 0
                    else:
                        print(f"⚠️  文章不属于目标账号，跳过")
                        self.crawled_urls.add(url)
                        consecutive_failures += 1
                else:
                    self.crawled_urls.add(url)  # 标记为已处理
                    consecutive_failures += 1

                # 每5篇文章后进行批次休息
                if crawled_count > 0 and crawled_count % 5 == 0:
                    print(f"🛡️  批次休息...")
                    self._smart_delay("batch_rest")

        # 保存历史记录
        self.save_history()

        # 生成智能总结
        self.generate_smart_summary(saved_files)

        print(f"\n🎉 智能分批爬取完成！")
        print(f"📊 本批成功爬取: {len(saved_files)} 篇文章")
        print(f"📈 当前成功率: {self.get_success_rate():.1f}%")
        print(f"📚 累计已爬取: {len(self.crawled_urls)} 个URL")
        print(f"📁 保存目录: {self.output_dir}")

        return saved_files

    def _is_target_account_article_smart(self, article: Dict) -> bool:
        """智能验证文章是否属于目标账号"""
        if not self.account_info.get('name'):
            return True

        target_name = self.account_info['name']
        article_author = article.get('author', '')
        article_title = article.get('title', '')
        article_url = article.get('url', '')

        # 检查作者名称
        if target_name in article_author or article_author in target_name:
            return True

        # 检查URL中的biz参数
        if self.account_info.get('biz'):
            target_biz = self.account_info['biz']
            if target_biz in article_url:
                return True

        # 避免明显的错误文章
        invalid_indicators = ['名誉保护', '投诉指引', '系统提示', '验证码']
        for indicator in invalid_indicators:
            if indicator in article_title:
                return False

        # 检查内容质量
        content_length = article.get('content_length', 0)
        if content_length < 200:  # 内容过短
            return False

        return True

    def generate_smart_summary(self, saved_files: List[str]):
        """生成智能爬取总结"""
        try:
            account_name = self.account_info.get('name', '未知公众号')
            success_rate = self.get_success_rate()

            summary_content = f"""# 智能分批微信爬虫 - 爬取总结

**公众号:** {account_name}
**爬取时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**本批文章数:** {len(saved_files)}
**累计文章数:** {len(self.articles)}
**保存目录:** {self.output_dir}

## 智能策略效果
- 🎯 当前成功率: {success_rate:.1f}%
- 📊 成功爬取: {self.success_count} 篇
- ❌ 失败次数: {self.failure_count} 次
- 🛡️ 保护触发: {self.protection_count} 次
- 🔄 延迟倍数: {self.current_delay_multiplier:.1f}

## 账号信息
- **名称:** {account_name}
- **BIZ参数:** {self.account_info.get('biz', '未获取')}
- **描述:** {self.account_info.get('description', '未获取')[:100]}

## 技术统计
- **总请求数:** {self.request_count}
- **已处理URL:** {len(self.crawled_urls)}
- **平均内容长度:** {sum(article.get('content_length', 0) for article in self.articles) // max(len(self.articles), 1)} 字符

## 本批文章列表

"""

            for i, article in enumerate(self.articles[-len(saved_files):], 1):
                summary_content += f"{i}. **{article['title']}**\n"
                summary_content += f"   - 作者: {article['author']}\n"
                summary_content += f"   - 发布时间: {article['publish_time']}\n"
                summary_content += f"   - 内容长度: {article.get('content_length', 0)} 字符\n"
                summary_content += f"   - 链接: {article['url']}\n\n"

            summary_content += f"""
## 保存的文件

"""

            for i, filepath in enumerate(saved_files, 1):
                filename = Path(filepath).name
                summary_content += f"{i}. {filename}\n"

            summary_content += f"""

## 智能优化建议
"""

            if success_rate < 50:
                summary_content += "- ⚠️ 成功率较低，建议增加延迟时间\n"
                summary_content += "- 💡 建议减少单批爬取数量\n"
            elif success_rate > 80:
                summary_content += "- ✅ 成功率良好，可以适当增加爬取速度\n"
                summary_content += "- 🚀 可以尝试增加单批爬取数量\n"

            if self.protection_count > 3:
                summary_content += "- 🛡️ 保护机制触发较多，建议进一步增加延迟\n"

            summary_content += f"""

---
*由智能分批微信爬虫生成 - 自适应速度控制*
"""

            summary_path = self.output_dir / f"智能爬取总结_{account_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"

            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary_content)

            print(f"📋 智能总结已保存: {summary_path.name}")

        except Exception as e:
            print(f"❌ 生成总结失败: {e}")


def main():
    """主函数"""
    print("=== 智能分批微信公众号文章爬虫 ===")
    print("优化速度控制，提高下载成功率，减少触发微信保护机制")
    print()

    start_url = input("请输入微信文章URL: ").strip()

    if not start_url:
        print("❌ URL不能为空")
        return

    if 'mp.weixin.qq.com' not in start_url:
        print("❌ 请输入有效的微信公众号文章URL")
        return

    max_articles = input("请输入本批要爬取的文章数量 (建议15以内): ").strip()
    try:
        max_articles = int(max_articles) if max_articles else 15
        if max_articles > 30:
            print("⚠️  建议单批不超过30篇，避免触发保护机制")
            max_articles = 30
    except ValueError:
        max_articles = 15

    skip_count = input("请输入要跳过的文章数量 (默认0): ").strip()
    try:
        skip_count = int(skip_count) if skip_count else 0
    except ValueError:
        skip_count = 0

    output_dir = input("请输入输出目录 (默认: C:/Users/<USER>/Documents/Obsidian Vault/微信好文): ").strip()
    if not output_dir:
        output_dir = "C:/Users/<USER>/Documents/Obsidian Vault/微信好文"

    # 创建智能爬虫实例
    crawler = SmartBatchWeChatCrawler(output_dir=output_dir)

    print(f"\n🧠 智能设置:")
    print(f"   📊 历史成功率: {crawler.get_success_rate():.1f}%")
    print(f"   🔄 当前延迟倍数: {crawler.current_delay_multiplier:.1f}")
    print(f"   ⏱️ 基础延迟: {crawler.base_delay}秒")
    print(f"   🛡️ 智能保护检测: 已启用")
    print(f"   📈 动态延迟调整: 已启用")

    try:
        # 开始智能分批爬取
        saved_files = crawler.crawl_articles_smart_batch(start_url, max_articles, skip_count)

        if saved_files:
            print(f"\n✅ 智能分批爬取成功完成!")
            print(f"📁 文件保存在: {output_dir}")
            print(f"📊 本批共保存 {len(saved_files)} 篇文章")
            print(f"📈 当前成功率: {crawler.get_success_rate():.1f}%")

            # 提供下一批建议
            if len(saved_files) > 0:
                next_skip = skip_count + len(saved_files)
                print(f"\n💡 继续爬取下一批建议:")
                print(f"   跳过数量: {next_skip}")
                print(f"   建议延迟倍数: {crawler.current_delay_multiplier:.1f}")
        else:
            print(f"\n❌ 智能爬取失败或无新文章")
            print(f"💡 建议:")
            print(f"   1. 检查网络连接")
            print(f"   2. 等待更长时间后重试")
            print(f"   3. 尝试不同的起始文章")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断了爬取过程")
    except Exception as e:
        print(f"\n❌ 爬取过程出错: {e}")


if __name__ == "__main__":
    main()